apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubevirt.io/generation: "3"
    kubevirt.io/install-strategy-identifier: c2053a4889fe65e8d368b5c232901c84fda8debe
    kubevirt.io/install-strategy-registry: registry.suse.com/suse/sles/15.6
    kubevirt.io/install-strategy-version: 1.4.0-150600.5.15.1
  creationTimestamp: "2025-05-15T10:08:53Z"
  generation: 1
  labels:
    app.kubernetes.io/component: kubevirt
    app.kubernetes.io/managed-by: virt-operator
    kubevirt.io: ""
  name: virtualmachineinstances.kubevirt.io
  resourceVersion: "7469"
  uid: 61f119a0-9858-432b-9c2b-00205ce8260c
spec:
  conversion:
    strategy: None
  group: kubevirt.io
  names:
    categories:
    - all
    kind: VirtualMachineInstance
    listKind: VirtualMachineInstanceList
    plural: virtualmachineinstances
    shortNames:
    - vmi
    - vmis
    singular: virtualmachineinstance
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .status.interfaces[0].ipAddress
      name: IP
      type: string
    - jsonPath: .status.nodeName
      name: NodeName
      type: string
    - jsonPath: .status.conditions[?(@.type=='Ready')].status
      name: Ready
      type: string
    - jsonPath: .status.conditions[?(@.type=='LiveMigratable')].status
      name: Live-Migratable
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=='Paused')].status
      name: Paused
      priority: 1
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: VirtualMachineInstance is *the* VirtualMachineInstance Definition.
          It represents a virtual machine in the runtime environment of kubernetes.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: VirtualMachineInstance Spec contains the VirtualMachineInstance
              specification.
            properties:
              accessCredentials:
                description: Specifies a set of public keys to inject into the vm
                  guest
                items:
                  description: |-
                    AccessCredential represents a credential source that can be used to
                    authorize remote access to the vm guest
                    Only one of its members may be specified.
                  properties:
                    sshPublicKey:
                      description: |-
                        SSHPublicKey represents the source and method of applying a ssh public
                        key into a guest virtual machine.
                      properties:
                        propagationMethod:
                          description: PropagationMethod represents how the public
                            key is injected into the vm guest.
                          properties:
                            configDrive:
                              description: |-
                                ConfigDrivePropagation means that the ssh public keys are injected
                                into the VM using metadata using the configDrive cloud-init provider
                              type: object
                            noCloud:
                              description: |-
                                NoCloudPropagation means that the ssh public keys are injected
                                into the VM using metadata using the noCloud cloud-init provider
                              type: object
                            qemuGuestAgent:
                              description: |-
                                QemuGuestAgentAccessCredentailPropagation means ssh public keys are
                                dynamically injected into the vm at runtime via the qemu guest agent.
                                This feature requires the qemu guest agent to be running within the guest.
                              properties:
                                users:
                                  description: |-
                                    Users represents a list of guest users that should have the ssh public keys
                                    added to their authorized_keys file.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: set
                              required:
                              - users
                              type: object
                          type: object
                        source:
                          description: Source represents where the public keys are
                            pulled from
                          properties:
                            secret:
                              description: Secret means that the access credential
                                is pulled from a kubernetes secret
                              properties:
                                secretName:
                                  description: SecretName represents the name of the
                                    secret in the VMI's namespace
                                  type: string
                              required:
                              - secretName
                              type: object
                          type: object
                      required:
                      - propagationMethod
                      - source
                      type: object
                    userPassword:
                      description: |-
                        UserPassword represents the source and method for applying a guest user's
                        password
                      properties:
                        propagationMethod:
                          description: propagationMethod represents how the user passwords
                            are injected into the vm guest.
                          properties:
                            qemuGuestAgent:
                              description: |-
                                QemuGuestAgentAccessCredentailPropagation means passwords are
                                dynamically injected into the vm at runtime via the qemu guest agent.
                                This feature requires the qemu guest agent to be running within the guest.
                              type: object
                          type: object
                        source:
                          description: Source represents where the user passwords
                            are pulled from
                          properties:
                            secret:
                              description: Secret means that the access credential
                                is pulled from a kubernetes secret
                              properties:
                                secretName:
                                  description: SecretName represents the name of the
                                    secret in the VMI's namespace
                                  type: string
                              required:
                              - secretName
                              type: object
                          type: object
                      required:
                      - propagationMethod
                      - source
                      type: object
                  type: object
                maxItems: 256
                type: array
                x-kubernetes-list-type: atomic
              affinity:
                description: If affinity is specifies, obey all the affinity rules
                properties:
                  nodeAffinity:
                    description: Describes node affinity scheduling rules for the
                      pod.
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          The scheduler will prefer to schedule pods to nodes that satisfy
                          the affinity expressions specified by this field, but it may choose
                          a node that violates one or more of the expressions. The node that is
                          most preferred is the one with the greatest sum of weights, i.e.
                          for each node that meets all of the scheduling requirements (resource
                          request, requiredDuringScheduling affinity expressions, etc.),
                          compute a sum by iterating through the elements of this field and adding
                          "weight" to the sum if the node matches the corresponding matchExpressions; the
                          node(s) with the highest sum are the most preferred.
                        items:
                          description: |-
                            An empty preferred scheduling term matches all objects with implicit weight 0
                            (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
                          properties:
                            preference:
                              description: A node selector term, associated with the
                                corresponding weight.
                              properties:
                                matchExpressions:
                                  description: A list of node selector requirements
                                    by node's labels.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  description: A list of node selector requirements
                                    by node's fields.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            weight:
                              description: Weight associated with matching the corresponding
                                nodeSelectorTerm, in the range 1-100.
                              format: int32
                              type: integer
                          required:
                          - preference
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          If the affinity requirements specified by this field are not met at
                          scheduling time, the pod will not be scheduled onto the node.
                          If the affinity requirements specified by this field cease to be met
                          at some point during pod execution (e.g. due to an update), the system
                          may or may not try to eventually evict the pod from its node.
                        properties:
                          nodeSelectorTerms:
                            description: Required. A list of node selector terms.
                              The terms are ORed.
                            items:
                              description: |-
                                A null or empty node selector term matches no objects. The requirements of
                                them are ANDed.
                                The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
                              properties:
                                matchExpressions:
                                  description: A list of node selector requirements
                                    by node's labels.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  description: A list of node selector requirements
                                    by node's fields.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            type: array
                            x-kubernetes-list-type: atomic
                        required:
                        - nodeSelectorTerms
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  podAffinity:
                    description: Describes pod affinity scheduling rules (e.g. co-locate
                      this pod in the same node, zone, etc. as some other pod(s)).
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          The scheduler will prefer to schedule pods to nodes that satisfy
                          the affinity expressions specified by this field, but it may choose
                          a node that violates one or more of the expressions. The node that is
                          most preferred is the one with the greatest sum of weights, i.e.
                          for each node that meets all of the scheduling requirements (resource
                          request, requiredDuringScheduling affinity expressions, etc.),
                          compute a sum by iterating through the elements of this field and adding
                          "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                          node(s) with the highest sum are the most preferred.
                        items:
                          description: The weights of all of the matched WeightedPodAffinityTerm
                            fields are added per-node to find the most preferred node(s)
                          properties:
                            podAffinityTerm:
                              description: Required. A pod affinity term, associated
                                with the corresponding weight.
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              description: |-
                                weight associated with matching the corresponding podAffinityTerm,
                                in the range 1-100.
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          If the affinity requirements specified by this field are not met at
                          scheduling time, the pod will not be scheduled onto the node.
                          If the affinity requirements specified by this field cease to be met
                          at some point during pod execution (e.g. due to a pod label update), the
                          system may or may not try to eventually evict the pod from its node.
                          When there are multiple elements, the lists of nodes corresponding to each
                          podAffinityTerm are intersected, i.e. all terms must be satisfied.
                        items:
                          description: |-
                            Defines a set of pods (namely those matching the labelSelector
                            relative to the given namespace(s)) that this pod should be
                            co-located (affinity) or not co-located (anti-affinity) with,
                            where co-located is defined as running on a node whose value of
                            the label with key <topologyKey> matches that of any node on which
                            a pod of the set of pods is running
                          properties:
                            labelSelector:
                              description: |-
                                A label query over a set of resources, in this case pods.
                                If it's null, this PodAffinityTerm matches with no Pods.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              description: |-
                                MatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              description: |-
                                MismatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              description: |-
                                A label query over the set of namespaces that the term applies to.
                                The term is applied to the union of the namespaces selected by this field
                                and the ones listed in the namespaces field.
                                null selector and null or empty namespaces list means "this pod's namespace".
                                An empty selector ({}) matches all namespaces.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                namespaces specifies a static list of namespace names that the term applies to.
                                The term is applied to the union of the namespaces listed in this field
                                and the ones selected by namespaceSelector.
                                null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              description: |-
                                This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                whose value of the label with key topologyKey matches that of any node on which any of the
                                selected pods is running.
                                Empty topologyKey is not allowed.
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  podAntiAffinity:
                    description: Describes pod anti-affinity scheduling rules (e.g.
                      avoid putting this pod in the same node, zone, etc. as some
                      other pod(s)).
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          The scheduler will prefer to schedule pods to nodes that satisfy
                          the anti-affinity expressions specified by this field, but it may choose
                          a node that violates one or more of the expressions. The node that is
                          most preferred is the one with the greatest sum of weights, i.e.
                          for each node that meets all of the scheduling requirements (resource
                          request, requiredDuringScheduling anti-affinity expressions, etc.),
                          compute a sum by iterating through the elements of this field and adding
                          "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                          node(s) with the highest sum are the most preferred.
                        items:
                          description: The weights of all of the matched WeightedPodAffinityTerm
                            fields are added per-node to find the most preferred node(s)
                          properties:
                            podAffinityTerm:
                              description: Required. A pod affinity term, associated
                                with the corresponding weight.
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              description: |-
                                weight associated with matching the corresponding podAffinityTerm,
                                in the range 1-100.
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          If the anti-affinity requirements specified by this field are not met at
                          scheduling time, the pod will not be scheduled onto the node.
                          If the anti-affinity requirements specified by this field cease to be met
                          at some point during pod execution (e.g. due to a pod label update), the
                          system may or may not try to eventually evict the pod from its node.
                          When there are multiple elements, the lists of nodes corresponding to each
                          podAffinityTerm are intersected, i.e. all terms must be satisfied.
                        items:
                          description: |-
                            Defines a set of pods (namely those matching the labelSelector
                            relative to the given namespace(s)) that this pod should be
                            co-located (affinity) or not co-located (anti-affinity) with,
                            where co-located is defined as running on a node whose value of
                            the label with key <topologyKey> matches that of any node on which
                            a pod of the set of pods is running
                          properties:
                            labelSelector:
                              description: |-
                                A label query over a set of resources, in this case pods.
                                If it's null, this PodAffinityTerm matches with no Pods.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              description: |-
                                MatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              description: |-
                                MismatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              description: |-
                                A label query over the set of namespaces that the term applies to.
                                The term is applied to the union of the namespaces selected by this field
                                and the ones listed in the namespaces field.
                                null selector and null or empty namespaces list means "this pod's namespace".
                                An empty selector ({}) matches all namespaces.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                namespaces specifies a static list of namespace names that the term applies to.
                                The term is applied to the union of the namespaces listed in this field
                                and the ones selected by namespaceSelector.
                                null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              description: |-
                                This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                whose value of the label with key topologyKey matches that of any node on which any of the
                                selected pods is running.
                                Empty topologyKey is not allowed.
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                type: object
              architecture:
                description: Specifies the architecture of the vm guest you are attempting
                  to run. Defaults to the compiled architecture of the KubeVirt components
                type: string
              dnsConfig:
                description: |-
                  Specifies the DNS parameters of a pod.
                  Parameters specified here will be merged to the generated DNS
                  configuration based on DNSPolicy.
                properties:
                  nameservers:
                    description: |-
                      A list of DNS name server IP addresses.
                      This will be appended to the base nameservers generated from DNSPolicy.
                      Duplicated nameservers will be removed.
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  options:
                    description: |-
                      A list of DNS resolver options.
                      This will be merged with the base options generated from DNSPolicy.
                      Duplicated entries will be removed. Resolution options given in Options
                      will override those that appear in the base DNSPolicy.
                    items:
                      description: PodDNSConfigOption defines DNS resolver options
                        of a pod.
                      properties:
                        name:
                          description: Required.
                          type: string
                        value:
                          type: string
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  searches:
                    description: |-
                      A list of DNS search domains for host-name lookup.
                      This will be appended to the base search paths generated from DNSPolicy.
                      Duplicated search paths will be removed.
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
              dnsPolicy:
                description: |-
                  Set DNS policy for the pod.
                  Defaults to "ClusterFirst".
                  Valid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'.
                  DNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy.
                  To have DNS options set along with hostNetwork, you have to specify DNS policy
                  explicitly to 'ClusterFirstWithHostNet'.
                type: string
              domain:
                description: Specification of the desired behavior of the VirtualMachineInstance
                  on the host.
                properties:
                  chassis:
                    description: Chassis specifies the chassis info passed to the
                      domain.
                    properties:
                      asset:
                        type: string
                      manufacturer:
                        type: string
                      serial:
                        type: string
                      sku:
                        type: string
                      version:
                        type: string
                    type: object
                  clock:
                    description: Clock sets the clock and timers of the vmi.
                    properties:
                      timer:
                        description: Timer specifies whih timers are attached to the
                          vmi.
                        properties:
                          hpet:
                            description: HPET (High Precision Event Timer) - multiple
                              timers with periodic interrupts.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                              tickPolicy:
                                description: |-
                                  TickPolicy determines what happens when QEMU misses a deadline for injecting a tick to the guest.
                                  One of "delay", "catchup", "merge", "discard".
                                type: string
                            type: object
                          hyperv:
                            description: Hyperv (Hypervclock) - lets guests read the
                              host’s wall clock time (paravirtualized). For windows
                              guests.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                            type: object
                          kvm:
                            description: "KVM \t(KVM clock) - lets guests read the
                              host’s wall clock time (paravirtualized). For linux
                              guests."
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                            type: object
                          pit:
                            description: PIT (Programmable Interval Timer) - a timer
                              with periodic interrupts.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                              tickPolicy:
                                description: |-
                                  TickPolicy determines what happens when QEMU misses a deadline for injecting a tick to the guest.
                                  One of "delay", "catchup", "discard".
                                type: string
                            type: object
                          rtc:
                            description: RTC (Real Time Clock) - a continuously running
                              timer with periodic interrupts.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                              tickPolicy:
                                description: |-
                                  TickPolicy determines what happens when QEMU misses a deadline for injecting a tick to the guest.
                                  One of "delay", "catchup".
                                type: string
                              track:
                                description: Track the guest or the wall clock.
                                type: string
                            type: object
                        type: object
                      timezone:
                        description: |-
                          Timezone sets the guest clock to the specified timezone.
                          Zone name follows the TZ environment variable format (e.g. 'America/New_York').
                        type: string
                      utc:
                        description: |-
                          UTC sets the guest clock to UTC on each boot. If an offset is specified,
                          guest changes to the clock will be kept during reboots and are not reset.
                        properties:
                          offsetSeconds:
                            description: |-
                              OffsetSeconds specifies an offset in seconds, relative to UTC. If set,
                              guest changes to the clock will be kept during reboots and not reset.
                            type: integer
                        type: object
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                  cpu:
                    description: CPU allow specified the detailed CPU topology inside
                      the vmi.
                    properties:
                      cores:
                        description: |-
                          Cores specifies the number of cores inside the vmi.
                          Must be a value greater or equal 1.
                        format: int32
                        type: integer
                      dedicatedCpuPlacement:
                        description: |-
                          DedicatedCPUPlacement requests the scheduler to place the VirtualMachineInstance on a node
                          with enough dedicated pCPUs and pin the vCPUs to it.
                        type: boolean
                      features:
                        description: Features specifies the CPU features list inside
                          the VMI.
                        items:
                          description: CPUFeature allows specifying a CPU feature.
                          properties:
                            name:
                              description: Name of the CPU feature
                              type: string
                            policy:
                              description: |-
                                Policy is the CPU feature attribute which can have the following attributes:
                                force    - The virtual CPU will claim the feature is supported regardless of it being supported by host CPU.
                                require  - Guest creation will fail unless the feature is supported by the host CPU or the hypervisor is able to emulate it.
                                optional - The feature will be supported by virtual CPU if and only if it is supported by host CPU.
                                disable  - The feature will not be supported by virtual CPU.
                                forbid   - Guest creation will fail if the feature is supported by host CPU.
                                Defaults to require
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                      isolateEmulatorThread:
                        description: |-
                          IsolateEmulatorThread requests one more dedicated pCPU to be allocated for the VMI to place
                          the emulator thread on it.
                        type: boolean
                      maxSockets:
                        description: |-
                          MaxSockets specifies the maximum amount of sockets that can
                          be hotplugged
                        format: int32
                        type: integer
                      model:
                        description: |-
                          Model specifies the CPU model inside the VMI.
                          List of available models https://github.com/libvirt/libvirt/tree/master/src/cpu_map.
                          It is possible to specify special cases like "host-passthrough" to get the same CPU as the node
                          and "host-model" to get CPU closest to the node one.
                          Defaults to host-model.
                        type: string
                      numa:
                        description: NUMA allows specifying settings for the guest
                          NUMA topology
                        properties:
                          guestMappingPassthrough:
                            description: |-
                              GuestMappingPassthrough will create an efficient guest topology based on host CPUs exclusively assigned to a pod.
                              The created topology ensures that memory and CPUs on the virtual numa nodes never cross boundaries of host numa nodes.
                            type: object
                        type: object
                      realtime:
                        description: Realtime instructs the virt-launcher to tune
                          the VMI for lower latency, optional for real time workloads
                        properties:
                          mask:
                            description: |-
                              Mask defines the vcpu mask expression that defines which vcpus are used for realtime. Format matches libvirt's expressions.
                              Example: "0-3,^1","0,2,3","2-3"
                            type: string
                        type: object
                      sockets:
                        description: |-
                          Sockets specifies the number of sockets inside the vmi.
                          Must be a value greater or equal 1.
                        format: int32
                        type: integer
                      threads:
                        description: |-
                          Threads specifies the number of threads inside the vmi.
                          Must be a value greater or equal 1.
                        format: int32
                        type: integer
                    type: object
                  devices:
                    description: Devices allows adding disks, network interfaces,
                      and others
                    properties:
                      autoattachGraphicsDevice:
                        description: |-
                          Whether to attach the default graphics device or not.
                          VNC will not be available if set to false. Defaults to true.
                        type: boolean
                      autoattachInputDevice:
                        description: |-
                          Whether to attach an Input Device.
                          Defaults to false.
                        type: boolean
                      autoattachMemBalloon:
                        description: |-
                          Whether to attach the Memory balloon device with default period.
                          Period can be adjusted in virt-config.
                          Defaults to true.
                        type: boolean
                      autoattachPodInterface:
                        description: Whether to attach a pod network interface. Defaults
                          to true.
                        type: boolean
                      autoattachSerialConsole:
                        description: |-
                          Whether to attach the default virtio-serial console or not.
                          Serial console access will not be available if set to false. Defaults to true.
                        type: boolean
                      autoattachVSOCK:
                        description: |-
                          Whether to attach the VSOCK CID to the VM or not.
                          VSOCK access will be available if set to true. Defaults to false.
                        type: boolean
                      blockMultiQueue:
                        description: |-
                          Whether or not to enable virtio multi-queue for block devices.
                          Defaults to false.
                        type: boolean
                      clientPassthrough:
                        description: To configure and access client devices such as
                          redirecting USB
                        type: object
                      disableHotplug:
                        description: DisableHotplug disabled the ability to hotplug
                          disks.
                        type: boolean
                      disks:
                        description: Disks describes disks, cdroms and luns which
                          are connected to the vmi.
                        items:
                          properties:
                            blockSize:
                              description: If specified, the virtual disk will be
                                presented with the given block sizes.
                              properties:
                                custom:
                                  description: CustomBlockSize represents the desired
                                    logical and physical block size for a VM disk.
                                  properties:
                                    logical:
                                      type: integer
                                    physical:
                                      type: integer
                                  required:
                                  - logical
                                  - physical
                                  type: object
                                matchVolume:
                                  description: Represents if a feature is enabled
                                    or disabled.
                                  properties:
                                    enabled:
                                      description: |-
                                        Enabled determines if the feature should be enabled or disabled on the guest.
                                        Defaults to true.
                                      type: boolean
                                  type: object
                              type: object
                            bootOrder:
                              description: |-
                                BootOrder is an integer value > 0, used to determine ordering of boot devices.
                                Lower values take precedence.
                                Each disk or interface that has a boot order must have a unique value.
                                Disks without a boot order are not tried if a disk with a boot order exists.
                              type: integer
                            cache:
                              description: |-
                                Cache specifies which kvm disk cache mode should be used.
                                Supported values are: CacheNone, CacheWriteThrough.
                              type: string
                            cdrom:
                              description: Attach a volume as a cdrom to the vmi.
                              properties:
                                bus:
                                  description: |-
                                    Bus indicates the type of disk device to emulate.
                                    supported values: virtio, sata, scsi.
                                  type: string
                                readonly:
                                  description: |-
                                    ReadOnly.
                                    Defaults to true.
                                  type: boolean
                                tray:
                                  description: |-
                                    Tray indicates if the tray of the device is open or closed.
                                    Allowed values are "open" and "closed".
                                    Defaults to closed.
                                  type: string
                              type: object
                            dedicatedIOThread:
                              description: |-
                                dedicatedIOThread indicates this disk should have an exclusive IO Thread.
                                Enabling this implies useIOThreads = true.
                                Defaults to false.
                              type: boolean
                            disk:
                              description: Attach a volume as a disk to the vmi.
                              properties:
                                bus:
                                  description: |-
                                    Bus indicates the type of disk device to emulate.
                                    supported values: virtio, sata, scsi, usb.
                                  type: string
                                pciAddress:
                                  description: 'If specified, the virtual disk will
                                    be placed on the guests pci address with the specified
                                    PCI address. For example: 0000:81:01.10'
                                  type: string
                                readonly:
                                  description: |-
                                    ReadOnly.
                                    Defaults to false.
                                  type: boolean
                              type: object
                            errorPolicy:
                              description: If specified, it can change the default
                                error policy (stop) for the disk
                              type: string
                            io:
                              description: |-
                                IO specifies which QEMU disk IO mode should be used.
                                Supported values are: native, default, threads.
                              type: string
                            lun:
                              description: Attach a volume as a LUN to the vmi.
                              properties:
                                bus:
                                  description: |-
                                    Bus indicates the type of disk device to emulate.
                                    supported values: virtio, sata, scsi.
                                  type: string
                                readonly:
                                  description: |-
                                    ReadOnly.
                                    Defaults to false.
                                  type: boolean
                                reservation:
                                  description: Reservation indicates if the disk needs
                                    to support the persistent reservation for the
                                    SCSI disk
                                  type: boolean
                              type: object
                            name:
                              description: Name is the device name
                              type: string
                            serial:
                              description: Serial provides the ability to specify
                                a serial number for the disk device.
                              type: string
                            shareable:
                              description: If specified the disk is made sharable
                                and multiple write from different VMs are permitted
                              type: boolean
                            tag:
                              description: If specified, disk address and its tag
                                will be provided to the guest via config drive metadata
                              type: string
                          required:
                          - name
                          type: object
                        maxItems: 256
                        type: array
                      downwardMetrics:
                        description: DownwardMetrics creates a virtio serials for
                          exposing the downward metrics to the vmi.
                        type: object
                      filesystems:
                        description: Filesystems describes filesystem which is connected
                          to the vmi.
                        items:
                          properties:
                            name:
                              description: Name is the device name
                              type: string
                            virtiofs:
                              description: Virtiofs is supported
                              type: object
                          required:
                          - name
                          - virtiofs
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      gpus:
                        description: Whether to attach a GPU device to the vmi.
                        items:
                          properties:
                            deviceName:
                              type: string
                            name:
                              description: Name of the GPU device as exposed by a
                                device plugin
                              type: string
                            tag:
                              description: If specified, the virtual network interface
                                address and its tag will be provided to the guest
                                via config drive
                              type: string
                            virtualGPUOptions:
                              properties:
                                display:
                                  properties:
                                    enabled:
                                      description: |-
                                        Enabled determines if a display addapter backed by a vGPU should be enabled or disabled on the guest.
                                        Defaults to true.
                                      type: boolean
                                    ramFB:
                                      description: |-
                                        Enables a boot framebuffer, until the guest OS loads a real GPU driver
                                        Defaults to true.
                                      properties:
                                        enabled:
                                          description: |-
                                            Enabled determines if the feature should be enabled or disabled on the guest.
                                            Defaults to true.
                                          type: boolean
                                      type: object
                                  type: object
                              type: object
                          required:
                          - deviceName
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      hostDevices:
                        description: Whether to attach a host device to the vmi.
                        items:
                          properties:
                            deviceName:
                              description: DeviceName is the resource name of the
                                host device exposed by a device plugin
                              type: string
                            name:
                              type: string
                            tag:
                              description: If specified, the virtual network interface
                                address and its tag will be provided to the guest
                                via config drive
                              type: string
                          required:
                          - deviceName
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      inputs:
                        description: Inputs describe input devices
                        items:
                          properties:
                            bus:
                              description: |-
                                Bus indicates the bus of input device to emulate.
                                Supported values: virtio, usb.
                              type: string
                            name:
                              description: Name is the device name
                              type: string
                            type:
                              description: |-
                                Type indicated the type of input device.
                                Supported values: tablet.
                              type: string
                          required:
                          - name
                          - type
                          type: object
                        type: array
                      interfaces:
                        description: Interfaces describe network interfaces which
                          are added to the vmi.
                        items:
                          properties:
                            acpiIndex:
                              description: |-
                                If specified, the ACPI index is used to provide network interface device naming, that is stable across changes
                                in PCI addresses assigned to the device.
                                This value is required to be unique across all devices and be between 1 and (16*1024-1).
                              type: integer
                            binding:
                              description: |-
                                Binding specifies the binding plugin that will be used to connect the interface to the guest.
                                It provides an alternative to InterfaceBindingMethod.
                                version: 1alphav1
                              properties:
                                name:
                                  description: |-
                                    Name references to the binding name as denined in the kubevirt CR.
                                    version: 1alphav1
                                  type: string
                              required:
                              - name
                              type: object
                            bootOrder:
                              description: |-
                                BootOrder is an integer value > 0, used to determine ordering of boot devices.
                                Lower values take precedence.
                                Each interface or disk that has a boot order must have a unique value.
                                Interfaces without a boot order are not tried.
                              type: integer
                            bridge:
                              description: InterfaceBridge connects to a given network
                                via a linux bridge.
                              type: object
                            dhcpOptions:
                              description: If specified the network interface will
                                pass additional DHCP options to the VMI
                              properties:
                                bootFileName:
                                  description: If specified will pass option 67 to
                                    interface's DHCP server
                                  type: string
                                ntpServers:
                                  description: If specified will pass the configured
                                    NTP server to the VM via DHCP option 042.
                                  items:
                                    type: string
                                  type: array
                                privateOptions:
                                  description: 'If specified will pass extra DHCP
                                    options for private use, range: 224-254'
                                  items:
                                    description: DHCPExtraOptions defines Extra DHCP
                                      options for a VM.
                                    properties:
                                      option:
                                        description: |-
                                          Option is an Integer value from 224-254
                                          Required.
                                        type: integer
                                      value:
                                        description: |-
                                          Value is a String value for the Option provided
                                          Required.
                                        type: string
                                    required:
                                    - option
                                    - value
                                    type: object
                                  type: array
                                tftpServerName:
                                  description: If specified will pass option 66 to
                                    interface's DHCP server
                                  type: string
                              type: object
                            macAddress:
                              description: 'Interface MAC address. For example: de:ad:00:00:be:af
                                or DE-AD-00-00-BE-AF.'
                              type: string
                            macvtap:
                              description: |-
                                DeprecatedMacvtap is an alias to the deprecated Macvtap interface,
                                please refer to Kubevirt user guide for alternatives.
                                Deprecated: Removed in v1.3
                              type: object
                            masquerade:
                              description: InterfaceMasquerade connects to a given
                                network using netfilter rules to nat the traffic.
                              type: object
                            model:
                              description: |-
                                Interface model.
                                One of: e1000, e1000e, igb, ne2k_pci, pcnet, rtl8139, virtio.
                                Defaults to virtio.
                              type: string
                            name:
                              description: |-
                                Logical name of the interface as well as a reference to the associated networks.
                                Must match the Name of a Network.
                              type: string
                            passt:
                              description: |-
                                DeprecatedPasst is an alias to the deprecated Passt interface,
                                please refer to Kubevirt user guide for alternatives.
                                Deprecated: Removed in v1.3
                              type: object
                            pciAddress:
                              description: 'If specified, the virtual network interface
                                will be placed on the guests pci address with the
                                specified PCI address. For example: 0000:81:01.10'
                              type: string
                            ports:
                              description: List of ports to be forwarded to the virtual
                                machine.
                              items:
                                description: |-
                                  Port represents a port to expose from the virtual machine.
                                  Default protocol TCP.
                                  The port field is mandatory
                                properties:
                                  name:
                                    description: |-
                                      If specified, this must be an IANA_SVC_NAME and unique within the pod. Each
                                      named port in a pod must have a unique name. Name for the port that can be
                                      referred to by services.
                                    type: string
                                  port:
                                    description: |-
                                      Number of port to expose for the virtual machine.
                                      This must be a valid port number, 0 < x < 65536.
                                    format: int32
                                    type: integer
                                  protocol:
                                    description: |-
                                      Protocol for port. Must be UDP or TCP.
                                      Defaults to "TCP".
                                    type: string
                                required:
                                - port
                                type: object
                              type: array
                            slirp:
                              description: |-
                                DeprecatedSlirp is an alias to the deprecated Slirp interface
                                Deprecated: Removed in v1.3
                              type: object
                            sriov:
                              description: InterfaceSRIOV connects to a given network
                                by passing-through an SR-IOV PCI device via vfio.
                              type: object
                            state:
                              description: |-
                                State represents the requested operational state of the interface.
                                The (only) value supported is 'absent', expressing a request to remove the interface.
                              type: string
                            tag:
                              description: If specified, the virtual network interface
                                address and its tag will be provided to the guest
                                via config drive
                              type: string
                          required:
                          - name
                          type: object
                        maxItems: 256
                        type: array
                      logSerialConsole:
                        description: |-
                          Whether to log the auto-attached default serial console or not.
                          Serial console logs will be collect to a file and then streamed from a named 'guest-console-log'.
                          Not relevant if autoattachSerialConsole is disabled.
                          Defaults to cluster wide setting on VirtualMachineOptions.
                        type: boolean
                      networkInterfaceMultiqueue:
                        description: If specified, virtual network interfaces configured
                          with a virtio bus will also enable the vhost multiqueue
                          feature for network devices. The number of queues created
                          depends on additional factors of the VirtualMachineInstance,
                          like the number of guest CPUs.
                        type: boolean
                      rng:
                        description: Whether to have random number generator from
                          host
                        type: object
                      sound:
                        description: Whether to emulate a sound device.
                        properties:
                          model:
                            description: |-
                              We only support ich9 or ac97.
                              If SoundDevice is not set: No sound card is emulated.
                              If SoundDevice is set but Model is not: ich9
                            type: string
                          name:
                            description: User's defined name for this sound device
                            type: string
                        required:
                        - name
                        type: object
                      tpm:
                        description: Whether to emulate a TPM device.
                        properties:
                          persistent:
                            description: |-
                              Persistent indicates the state of the TPM device should be kept accross reboots
                              Defaults to false
                            type: boolean
                        type: object
                      useVirtioTransitional:
                        description: |-
                          Fall back to legacy virtio 0.9 support if virtio bus is selected on devices.
                          This is helpful for old machines like CentOS6 or RHEL6 which
                          do not understand virtio_non_transitional (virtio 1.0).
                        type: boolean
                      watchdog:
                        description: Watchdog describes a watchdog device which can
                          be added to the vmi.
                        properties:
                          i6300esb:
                            description: i6300esb watchdog device.
                            properties:
                              action:
                                description: |-
                                  The action to take. Valid values are poweroff, reset, shutdown.
                                  Defaults to reset.
                                type: string
                            type: object
                          name:
                            description: Name of the watchdog.
                            type: string
                        required:
                        - name
                        type: object
                    type: object
                  features:
                    description: Features like acpi, apic, hyperv, smm.
                    properties:
                      acpi:
                        description: |-
                          ACPI enables/disables ACPI inside the guest.
                          Defaults to enabled.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                        type: object
                      apic:
                        description: Defaults to the machine type setting.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                          endOfInterrupt:
                            description: |-
                              EndOfInterrupt enables the end of interrupt notification in the guest.
                              Defaults to false.
                            type: boolean
                        type: object
                      hyperv:
                        description: Defaults to the machine type setting.
                        properties:
                          evmcs:
                            description: |-
                              EVMCS Speeds up L2 vmexits, but disables other virtualization features. Requires vapic.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          frequencies:
                            description: |-
                              Frequencies improves the TSC clock source handling for Hyper-V on KVM.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          ipi:
                            description: |-
                              IPI improves performances in overcommited environments. Requires vpindex.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          reenlightenment:
                            description: |-
                              Reenlightenment enables the notifications on TSC frequency changes.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          relaxed:
                            description: |-
                              Relaxed instructs the guest OS to disable watchdog timeouts.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          reset:
                            description: |-
                              Reset enables Hyperv reboot/reset for the vmi. Requires synic.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          runtime:
                            description: |-
                              Runtime improves the time accounting to improve scheduling in the guest.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          spinlocks:
                            description: Spinlocks allows to configure the spinlock
                              retry attempts.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                              spinlocks:
                                description: |-
                                  Retries indicates the number of retries.
                                  Must be a value greater or equal 4096.
                                  Defaults to 4096.
                                format: int32
                                type: integer
                            type: object
                          synic:
                            description: |-
                              SyNIC enables the Synthetic Interrupt Controller.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          synictimer:
                            description: |-
                              SyNICTimer enables Synthetic Interrupt Controller Timers, reducing CPU load.
                              Defaults to the machine type setting.
                            properties:
                              direct:
                                description: Represents if a feature is enabled or
                                  disabled.
                                properties:
                                  enabled:
                                    description: |-
                                      Enabled determines if the feature should be enabled or disabled on the guest.
                                      Defaults to true.
                                    type: boolean
                                type: object
                              enabled:
                                type: boolean
                            type: object
                          tlbflush:
                            description: |-
                              TLBFlush improves performances in overcommited environments. Requires vpindex.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          vapic:
                            description: |-
                              VAPIC improves the paravirtualized handling of interrupts.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          vendorid:
                            description: |-
                              VendorID allows setting the hypervisor vendor id.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                              vendorid:
                                description: |-
                                  VendorID sets the hypervisor vendor id, visible to the vmi.
                                  String up to twelve characters.
                                type: string
                            type: object
                          vpindex:
                            description: |-
                              VPIndex enables the Virtual Processor Index to help windows identifying virtual processors.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                        type: object
                      hypervPassthrough:
                        description: |-
                          This enables all supported hyperv flags automatically.
                          Bear in mind that if this enabled hyperV features cannot
                          be enabled explicitly. In addition, a Virtual Machine
                          using it will be non-migratable.
                        properties:
                          enabled:
                            type: boolean
                        type: object
                      kvm:
                        description: Configure how KVM presence is exposed to the
                          guest.
                        properties:
                          hidden:
                            description: |-
                              Hide the KVM hypervisor from standard MSR based discovery.
                              Defaults to false
                            type: boolean
                        type: object
                      pvspinlock:
                        description: |-
                          Notify the guest that the host supports paravirtual spinlocks.
                          For older kernels this feature should be explicitly disabled.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                        type: object
                      smm:
                        description: |-
                          SMM enables/disables System Management Mode.
                          TSEG not yet implemented.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                        type: object
                    type: object
                  firmware:
                    description: Firmware.
                    properties:
                      acpi:
                        description: Information that can be set in the ACPI table
                        properties:
                          slicNameRef:
                            description: |-
                              SlicNameRef should match the volume name of a secret object. The data in the secret should
                              be a binary blob that follows the ACPI SLIC standard, see:
                              https://learn.microsoft.com/en-us/previous-versions/windows/hardware/design/dn653305(v=vs.85)
                            type: string
                        type: object
                      bootloader:
                        description: Settings to control the bootloader that is used.
                        properties:
                          bios:
                            description: If set (default), BIOS will be used.
                            properties:
                              useSerial:
                                description: If set, the BIOS output will be transmitted
                                  over serial
                                type: boolean
                            type: object
                          efi:
                            description: If set, EFI will be used instead of BIOS.
                            properties:
                              persistent:
                                description: |-
                                  If set to true, Persistent will persist the EFI NVRAM across reboots.
                                  Defaults to false
                                type: boolean
                              secureBoot:
                                description: |-
                                  If set, SecureBoot will be enabled and the OVMF roms will be swapped for
                                  SecureBoot-enabled ones.
                                  Requires SMM to be enabled.
                                  Defaults to true
                                type: boolean
                            type: object
                        type: object
                      kernelBoot:
                        description: Settings to set the kernel for booting.
                        properties:
                          container:
                            description: Container defines the container that containes
                              kernel artifacts
                            properties:
                              image:
                                description: Image that contains initrd / kernel files.
                                type: string
                              imagePullPolicy:
                                description: |-
                                  Image pull policy.
                                  One of Always, Never, IfNotPresent.
                                  Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
                                  Cannot be updated.
                                  More info: https://kubernetes.io/docs/concepts/containers/images#updating-images
                                type: string
                              imagePullSecret:
                                description: ImagePullSecret is the name of the Docker
                                  registry secret required to pull the image. The
                                  secret must already exist.
                                type: string
                              initrdPath:
                                description: the fully-qualified path to the ramdisk
                                  image in the host OS
                                type: string
                              kernelPath:
                                description: The fully-qualified path to the kernel
                                  image in the host OS
                                type: string
                            required:
                            - image
                            type: object
                          kernelArgs:
                            description: Arguments to be passed to the kernel at boot
                              time
                            type: string
                        type: object
                      serial:
                        description: The system-serial-number in SMBIOS
                        type: string
                      uuid:
                        description: |-
                          UUID reported by the vmi bios.
                          Defaults to a random generated uid.
                        type: string
                    type: object
                  ioThreadsPolicy:
                    description: |-
                      Controls whether or not disks will share IOThreads.
                      Omitting IOThreadsPolicy disables use of IOThreads.
                      One of: shared, auto
                    type: string
                  launchSecurity:
                    description: Launch Security setting of the vmi.
                    properties:
                      sev:
                        description: AMD Secure Encrypted Virtualization (SEV).
                        properties:
                          attestation:
                            description: If specified, run the attestation process
                              for a vmi.
                            type: object
                          dhCert:
                            description: Base64 encoded guest owner's Diffie-Hellman
                              key.
                            type: string
                          policy:
                            description: |-
                              Guest policy flags as defined in AMD SEV API specification.
                              Note: due to security reasons it is not allowed to enable guest debugging. Therefore NoDebug flag is not exposed to users and is always true.
                            properties:
                              encryptedState:
                                description: |-
                                  SEV-ES is required.
                                  Defaults to false.
                                type: boolean
                            type: object
                          session:
                            description: Base64 encoded session blob.
                            type: string
                        type: object
                    type: object
                  machine:
                    description: Machine type.
                    properties:
                      type:
                        description: QEMU machine type is the actual chipset of the
                          VirtualMachineInstance.
                        type: string
                    type: object
                  memory:
                    description: Memory allow specifying the VMI memory features.
                    properties:
                      guest:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Guest allows to specifying the amount of memory which is visible inside the Guest OS.
                          The Guest must lie between Requests and Limits from the resources section.
                          Defaults to the requested memory in the resources section if not specified.
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      hugepages:
                        description: Hugepages allow to use hugepages for the VirtualMachineInstance
                          instead of regular memory.
                        properties:
                          pageSize:
                            description: PageSize specifies the hugepage size, for
                              x86_64 architecture valid values are 1Gi and 2Mi.
                            type: string
                        type: object
                      maxGuest:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          MaxGuest allows to specify the maximum amount of memory which is visible inside the Guest OS.
                          The delta between MaxGuest and Guest is the amount of memory that can be hot(un)plugged.
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                    type: object
                  resources:
                    description: Resources describes the Compute Resources required
                      by this vmi.
                    properties:
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Limits describes the maximum amount of compute resources allowed.
                          Valid resource keys are "memory" and "cpu".
                        type: object
                      overcommitGuestOverhead:
                        description: |-
                          Don't ask the scheduler to take the guest-management overhead into account. Instead
                          put the overhead only into the container's memory limit. This can lead to crashes if
                          all memory is in use on a node. Defaults to false.
                        type: boolean
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Requests is a description of the initial vmi resources.
                          Valid resource keys are "memory" and "cpu".
                        type: object
                    type: object
                required:
                - devices
                type: object
              evictionStrategy:
                description: |-
                  EvictionStrategy describes the strategy to follow when a node drain occurs.
                  The possible options are:
                  - "None": No action will be taken, according to the specified 'RunStrategy' the VirtualMachine will be restarted or shutdown.
                  - "LiveMigrate": the VirtualMachineInstance will be migrated instead of being shutdown.
                  - "LiveMigrateIfPossible": the same as "LiveMigrate" but only if the VirtualMachine is Live-Migratable, otherwise it will behave as "None".
                  - "External": the VirtualMachineInstance will be protected by a PDB and 'vmi.Status.EvacuationNodeName' will be set on eviction. This is mainly useful for cluster-api-provider-kubevirt (capk) which needs a way for VMI's to be blocked from eviction, yet signal capk that eviction has been called on the VMI so the capk controller can handle tearing the VMI down. Details can be found in the commit description https://github.com/kubevirt/kubevirt/commit/c1d77face705c8b126696bac9a3ee3825f27f1fa.
                type: string
              hostname:
                description: |-
                  Specifies the hostname of the vmi
                  If not specified, the hostname will be set to the name of the vmi, if dhcp or cloud-init is configured properly.
                type: string
              livenessProbe:
                description: |-
                  Periodic probe of VirtualMachineInstance liveness.
                  VirtualmachineInstances will be stopped if the probe fails.
                  Cannot be updated.
                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                properties:
                  exec:
                    description: |-
                      One and only one of the following should be specified.
                      Exec specifies the action to take, it will be executed on the guest through the qemu-guest-agent.
                      If the guest agent is not available, this probe will fail.
                    properties:
                      command:
                        description: |-
                          Command is the command line to execute inside the container, the working directory for the
                          command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
                          not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
                          a shell, you need to explicitly call out to that shell.
                          Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  failureThreshold:
                    description: |-
                      Minimum consecutive failures for the probe to be considered failed after having succeeded.
                      Defaults to 3. Minimum value is 1.
                    format: int32
                    type: integer
                  guestAgentPing:
                    description: GuestAgentPing contacts the qemu-guest-agent for
                      availability checks.
                    type: object
                  httpGet:
                    description: HTTPGet specifies the http request to perform.
                    properties:
                      host:
                        description: |-
                          Host name to connect to, defaults to the pod IP. You probably want to set
                          "Host" in httpHeaders instead.
                        type: string
                      httpHeaders:
                        description: Custom headers to set in the request. HTTP allows
                          repeated headers.
                        items:
                          description: HTTPHeader describes a custom header to be
                            used in HTTP probes
                          properties:
                            name:
                              description: |-
                                The header field name.
                                This will be canonicalized upon output, so case-variant names will be understood as the same header.
                              type: string
                            value:
                              description: The header field value
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      path:
                        description: Path to access on the HTTP server.
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Name or number of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                      scheme:
                        description: |-
                          Scheme to use for connecting to the host.
                          Defaults to HTTP.
                        type: string
                    required:
                    - port
                    type: object
                  initialDelaySeconds:
                    description: |-
                      Number of seconds after the VirtualMachineInstance has started before liveness probes are initiated.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                  periodSeconds:
                    description: |-
                      How often (in seconds) to perform the probe.
                      Default to 10 seconds. Minimum value is 1.
                    format: int32
                    type: integer
                  successThreshold:
                    description: |-
                      Minimum consecutive successes for the probe to be considered successful after having failed.
                      Defaults to 1. Must be 1 for liveness. Minimum value is 1.
                    format: int32
                    type: integer
                  tcpSocket:
                    description: |-
                      TCPSocket specifies an action involving a TCP port.
                      TCP hooks not yet supported
                    properties:
                      host:
                        description: 'Optional: Host name to connect to, defaults
                          to the pod IP.'
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Number or name of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                    required:
                    - port
                    type: object
                  timeoutSeconds:
                    description: |-
                      Number of seconds after which the probe times out.
                      For exec probes the timeout fails the probe but does not terminate the command running on the guest.
                      This means a blocking command can result in an increasing load on the guest.
                      A small buffer will be added to the resulting workload exec probe to compensate for delays
                      caused by the qemu guest exec mechanism.
                      Defaults to 1 second. Minimum value is 1.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                type: object
              networks:
                description: List of networks that can be attached to a vm's virtual
                  interface.
                items:
                  description: Network represents a network type and a resource that
                    should be connected to the vm.
                  properties:
                    multus:
                      description: Represents the multus cni network.
                      properties:
                        default:
                          description: |-
                            Select the default network and add it to the
                            multus-cni.io/default-network annotation.
                          type: boolean
                        networkName:
                          description: |-
                            References to a NetworkAttachmentDefinition CRD object. Format:
                            <networkName>, <namespace>/<networkName>. If namespace is not
                            specified, VMI namespace is assumed.
                          type: string
                      required:
                      - networkName
                      type: object
                    name:
                      description: |-
                        Network name.
                        Must be a DNS_LABEL and unique within the vm.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      type: string
                    pod:
                      description: Represents the stock pod network interface.
                      properties:
                        vmIPv6NetworkCIDR:
                          description: |-
                            IPv6 CIDR for the vm network.
                            Defaults to fd10:0:2::/120 if not specified.
                          type: string
                        vmNetworkCIDR:
                          description: |-
                            CIDR for vm network.
                            Default ********/24 if not specified.
                          type: string
                      type: object
                  required:
                  - name
                  type: object
                maxItems: 256
                type: array
              nodeSelector:
                additionalProperties:
                  type: string
                description: |-
                  NodeSelector is a selector which must be true for the vmi to fit on a node.
                  Selector which must match a node's labels for the vmi to be scheduled on that node.
                  More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
                type: object
              priorityClassName:
                description: |-
                  If specified, indicates the pod's priority.
                  If not specified, the pod priority will be default or zero if there is no
                  default.
                type: string
              readinessProbe:
                description: |-
                  Periodic probe of VirtualMachineInstance service readiness.
                  VirtualmachineInstances will be removed from service endpoints if the probe fails.
                  Cannot be updated.
                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                properties:
                  exec:
                    description: |-
                      One and only one of the following should be specified.
                      Exec specifies the action to take, it will be executed on the guest through the qemu-guest-agent.
                      If the guest agent is not available, this probe will fail.
                    properties:
                      command:
                        description: |-
                          Command is the command line to execute inside the container, the working directory for the
                          command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
                          not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
                          a shell, you need to explicitly call out to that shell.
                          Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  failureThreshold:
                    description: |-
                      Minimum consecutive failures for the probe to be considered failed after having succeeded.
                      Defaults to 3. Minimum value is 1.
                    format: int32
                    type: integer
                  guestAgentPing:
                    description: GuestAgentPing contacts the qemu-guest-agent for
                      availability checks.
                    type: object
                  httpGet:
                    description: HTTPGet specifies the http request to perform.
                    properties:
                      host:
                        description: |-
                          Host name to connect to, defaults to the pod IP. You probably want to set
                          "Host" in httpHeaders instead.
                        type: string
                      httpHeaders:
                        description: Custom headers to set in the request. HTTP allows
                          repeated headers.
                        items:
                          description: HTTPHeader describes a custom header to be
                            used in HTTP probes
                          properties:
                            name:
                              description: |-
                                The header field name.
                                This will be canonicalized upon output, so case-variant names will be understood as the same header.
                              type: string
                            value:
                              description: The header field value
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      path:
                        description: Path to access on the HTTP server.
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Name or number of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                      scheme:
                        description: |-
                          Scheme to use for connecting to the host.
                          Defaults to HTTP.
                        type: string
                    required:
                    - port
                    type: object
                  initialDelaySeconds:
                    description: |-
                      Number of seconds after the VirtualMachineInstance has started before liveness probes are initiated.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                  periodSeconds:
                    description: |-
                      How often (in seconds) to perform the probe.
                      Default to 10 seconds. Minimum value is 1.
                    format: int32
                    type: integer
                  successThreshold:
                    description: |-
                      Minimum consecutive successes for the probe to be considered successful after having failed.
                      Defaults to 1. Must be 1 for liveness. Minimum value is 1.
                    format: int32
                    type: integer
                  tcpSocket:
                    description: |-
                      TCPSocket specifies an action involving a TCP port.
                      TCP hooks not yet supported
                    properties:
                      host:
                        description: 'Optional: Host name to connect to, defaults
                          to the pod IP.'
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Number or name of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                    required:
                    - port
                    type: object
                  timeoutSeconds:
                    description: |-
                      Number of seconds after which the probe times out.
                      For exec probes the timeout fails the probe but does not terminate the command running on the guest.
                      This means a blocking command can result in an increasing load on the guest.
                      A small buffer will be added to the resulting workload exec probe to compensate for delays
                      caused by the qemu guest exec mechanism.
                      Defaults to 1 second. Minimum value is 1.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                type: object
              schedulerName:
                description: |-
                  If specified, the VMI will be dispatched by specified scheduler.
                  If not specified, the VMI will be dispatched by default scheduler.
                type: string
              startStrategy:
                description: StartStrategy can be set to "Paused" if Virtual Machine
                  should be started in paused state.
                type: string
              subdomain:
                description: |-
                  If specified, the fully qualified vmi hostname will be "<hostname>.<subdomain>.<pod namespace>.svc.<cluster domain>".
                  If not specified, the vmi will not have a domainname at all. The DNS entry will resolve to the vmi,
                  no matter if the vmi itself can pick up a hostname.
                type: string
              terminationGracePeriodSeconds:
                description: Grace period observed after signalling a VirtualMachineInstance
                  to stop after which the VirtualMachineInstance is force terminated.
                format: int64
                type: integer
              tolerations:
                description: If toleration is specified, obey all the toleration rules.
                items:
                  description: |-
                    The pod this Toleration is attached to tolerates any taint that matches
                    the triple <key,value,effect> using the matching operator <operator>.
                  properties:
                    effect:
                      description: |-
                        Effect indicates the taint effect to match. Empty means match all taint effects.
                        When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
                      type: string
                    key:
                      description: |-
                        Key is the taint key that the toleration applies to. Empty means match all taint keys.
                        If the key is empty, operator must be Exists; this combination means to match all values and all keys.
                      type: string
                    operator:
                      description: |-
                        Operator represents a key's relationship to the value.
                        Valid operators are Exists and Equal. Defaults to Equal.
                        Exists is equivalent to wildcard for value, so that a pod can
                        tolerate all taints of a particular category.
                      type: string
                    tolerationSeconds:
                      description: |-
                        TolerationSeconds represents the period of time the toleration (which must be
                        of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
                        it is not set, which means tolerate the taint forever (do not evict). Zero and
                        negative values will be treated as 0 (evict immediately) by the system.
                      format: int64
                      type: integer
                    value:
                      description: |-
                        Value is the taint value the toleration matches to.
                        If the operator is Exists, the value should be empty, otherwise just a regular string.
                      type: string
                  type: object
                type: array
              topologySpreadConstraints:
                description: |-
                  TopologySpreadConstraints describes how a group of VMIs will be spread across a given topology
                  domains. K8s scheduler will schedule VMI pods in a way which abides by the constraints.
                items:
                  description: TopologySpreadConstraint specifies how to spread matching
                    pods among the given topology.
                  properties:
                    labelSelector:
                      description: |-
                        LabelSelector is used to find matching pods.
                        Pods that match this label selector are counted to determine the number of pods
                        in their corresponding topology domain.
                      properties:
                        matchExpressions:
                          description: matchExpressions is a list of label selector
                            requirements. The requirements are ANDed.
                          items:
                            description: |-
                              A label selector requirement is a selector that contains values, a key, and an operator that
                              relates the key and values.
                            properties:
                              key:
                                description: key is the label key that the selector
                                  applies to.
                                type: string
                              operator:
                                description: |-
                                  operator represents a key's relationship to a set of values.
                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                type: string
                              values:
                                description: |-
                                  values is an array of string values. If the operator is In or NotIn,
                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                  the values array must be empty. This array is replaced during a strategic
                                  merge patch.
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - key
                            - operator
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        matchLabels:
                          additionalProperties:
                            type: string
                          description: |-
                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                          type: object
                      type: object
                      x-kubernetes-map-type: atomic
                    matchLabelKeys:
                      description: |-
                        MatchLabelKeys is a set of pod label keys to select the pods over which
                        spreading will be calculated. The keys are used to lookup values from the
                        incoming pod labels, those key-value labels are ANDed with labelSelector
                        to select the group of existing pods over which spreading will be calculated
                        for the incoming pod. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector.
                        MatchLabelKeys cannot be set when LabelSelector isn't set.
                        Keys that don't exist in the incoming pod labels will
                        be ignored. A null or empty list means only match against labelSelector.

                        This is a beta field and requires the MatchLabelKeysInPodTopologySpread feature gate to be enabled (enabled by default).
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    maxSkew:
                      description: |-
                        MaxSkew describes the degree to which pods may be unevenly distributed.
                        When 'whenUnsatisfiable=DoNotSchedule', it is the maximum permitted difference
                        between the number of matching pods in the target topology and the global minimum.
                        The global minimum is the minimum number of matching pods in an eligible domain
                        or zero if the number of eligible domains is less than MinDomains.
                        For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same
                        labelSelector spread as 2/2/1:
                        In this case, the global minimum is 1.
                        | zone1 | zone2 | zone3 |
                        |  P P  |  P P  |   P   |
                        - if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 2/2/2;
                        scheduling it onto zone1(zone2) would make the ActualSkew(3-1) on zone1(zone2)
                        violate MaxSkew(1).
                        - if MaxSkew is 2, incoming pod can be scheduled onto any zone.
                        When 'whenUnsatisfiable=ScheduleAnyway', it is used to give higher precedence
                        to topologies that satisfy it.
                        It's a required field. Default value is 1 and 0 is not allowed.
                      format: int32
                      type: integer
                    minDomains:
                      description: |-
                        MinDomains indicates a minimum number of eligible domains.
                        When the number of eligible domains with matching topology keys is less than minDomains,
                        Pod Topology Spread treats "global minimum" as 0, and then the calculation of Skew is performed.
                        And when the number of eligible domains with matching topology keys equals or greater than minDomains,
                        this value has no effect on scheduling.
                        As a result, when the number of eligible domains is less than minDomains,
                        scheduler won't schedule more than maxSkew Pods to those domains.
                        If value is nil, the constraint behaves as if MinDomains is equal to 1.
                        Valid values are integers greater than 0.
                        When value is not nil, WhenUnsatisfiable must be DoNotSchedule.

                        For example, in a 3-zone cluster, MaxSkew is set to 2, MinDomains is set to 5 and pods with the same
                        labelSelector spread as 2/2/2:
                        | zone1 | zone2 | zone3 |
                        |  P P  |  P P  |  P P  |
                        The number of domains is less than 5(MinDomains), so "global minimum" is treated as 0.
                        In this situation, new pod with the same labelSelector cannot be scheduled,
                        because computed skew will be 3(3 - 0) if new Pod is scheduled to any of the three zones,
                        it will violate MaxSkew.
                      format: int32
                      type: integer
                    nodeAffinityPolicy:
                      description: |-
                        NodeAffinityPolicy indicates how we will treat Pod's nodeAffinity/nodeSelector
                        when calculating pod topology spread skew. Options are:
                        - Honor: only nodes matching nodeAffinity/nodeSelector are included in the calculations.
                        - Ignore: nodeAffinity/nodeSelector are ignored. All nodes are included in the calculations.

                        If this value is nil, the behavior is equivalent to the Honor policy.
                        This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.
                      type: string
                    nodeTaintsPolicy:
                      description: |-
                        NodeTaintsPolicy indicates how we will treat node taints when calculating
                        pod topology spread skew. Options are:
                        - Honor: nodes without taints, along with tainted nodes for which the incoming pod
                        has a toleration, are included.
                        - Ignore: node taints are ignored. All nodes are included.

                        If this value is nil, the behavior is equivalent to the Ignore policy.
                        This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.
                      type: string
                    topologyKey:
                      description: |-
                        TopologyKey is the key of node labels. Nodes that have a label with this key
                        and identical values are considered to be in the same topology.
                        We consider each <key, value> as a "bucket", and try to put balanced number
                        of pods into each bucket.
                        We define a domain as a particular instance of a topology.
                        Also, we define an eligible domain as a domain whose nodes meet the requirements of
                        nodeAffinityPolicy and nodeTaintsPolicy.
                        e.g. If TopologyKey is "kubernetes.io/hostname", each Node is a domain of that topology.
                        And, if TopologyKey is "topology.kubernetes.io/zone", each zone is a domain of that topology.
                        It's a required field.
                      type: string
                    whenUnsatisfiable:
                      description: |-
                        WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy
                        the spread constraint.
                        - DoNotSchedule (default) tells the scheduler not to schedule it.
                        - ScheduleAnyway tells the scheduler to schedule the pod in any location,
                          but giving higher precedence to topologies that would help reduce the
                          skew.
                        A constraint is considered "Unsatisfiable" for an incoming pod
                        if and only if every possible node assignment for that pod would violate
                        "MaxSkew" on some topology.
                        For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same
                        labelSelector spread as 3/1/1:
                        | zone1 | zone2 | zone3 |
                        | P P P |   P   |   P   |
                        If WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled
                        to zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies
                        MaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler
                        won't make it *more* imbalanced.
                        It's a required field.
                      type: string
                  required:
                  - maxSkew
                  - topologyKey
                  - whenUnsatisfiable
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - topologyKey
                - whenUnsatisfiable
                x-kubernetes-list-type: map
              volumes:
                description: List of volumes that can be mounted by disks belonging
                  to the vmi.
                items:
                  description: Volume represents a named volume in a vmi.
                  properties:
                    cloudInitConfigDrive:
                      description: |-
                        CloudInitConfigDrive represents a cloud-init Config Drive user-data source.
                        The Config Drive data will be added as a disk to the vmi. A proper cloud-init installation is required inside the guest.
                        More info: https://cloudinit.readthedocs.io/en/latest/topics/datasources/configdrive.html
                      properties:
                        networkData:
                          description: NetworkData contains config drive inline cloud-init
                            networkdata.
                          type: string
                        networkDataBase64:
                          description: NetworkDataBase64 contains config drive cloud-init
                            networkdata as a base64 encoded string.
                          type: string
                        networkDataSecretRef:
                          description: NetworkDataSecretRef references a k8s secret
                            that contains config drive networkdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        secretRef:
                          description: UserDataSecretRef references a k8s secret that
                            contains config drive userdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        userData:
                          description: UserData contains config drive inline cloud-init
                            userdata.
                          type: string
                        userDataBase64:
                          description: UserDataBase64 contains config drive cloud-init
                            userdata as a base64 encoded string.
                          type: string
                      type: object
                    cloudInitNoCloud:
                      description: |-
                        CloudInitNoCloud represents a cloud-init NoCloud user-data source.
                        The NoCloud data will be added as a disk to the vmi. A proper cloud-init installation is required inside the guest.
                        More info: http://cloudinit.readthedocs.io/en/latest/topics/datasources/nocloud.html
                      properties:
                        networkData:
                          description: NetworkData contains NoCloud inline cloud-init
                            networkdata.
                          type: string
                        networkDataBase64:
                          description: NetworkDataBase64 contains NoCloud cloud-init
                            networkdata as a base64 encoded string.
                          type: string
                        networkDataSecretRef:
                          description: NetworkDataSecretRef references a k8s secret
                            that contains NoCloud networkdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        secretRef:
                          description: UserDataSecretRef references a k8s secret that
                            contains NoCloud userdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        userData:
                          description: UserData contains NoCloud inline cloud-init
                            userdata.
                          type: string
                        userDataBase64:
                          description: UserDataBase64 contains NoCloud cloud-init
                            userdata as a base64 encoded string.
                          type: string
                      type: object
                    configMap:
                      description: |-
                        ConfigMapSource represents a reference to a ConfigMap in the same namespace.
                        More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-pod-configmap/
                      properties:
                        name:
                          default: ""
                          description: |-
                            Name of the referent.
                            This field is effectively required, but due to backwards compatibility is
                            allowed to be empty. Instances of this type with an empty value here are
                            almost certainly wrong.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                          type: string
                        optional:
                          description: Specify whether the ConfigMap or it's keys
                            must be defined
                          type: boolean
                        volumeLabel:
                          description: |-
                            The volume label of the resulting disk inside the VMI.
                            Different bootstrapping mechanisms require different values.
                            Typical values are "cidata" (cloud-init), "config-2" (cloud-init) or "OEMDRV" (kickstart).
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    containerDisk:
                      description: |-
                        ContainerDisk references a docker image, embedding a qcow or raw disk.
                        More info: https://kubevirt.gitbooks.io/user-guide/registry-disk.html
                      properties:
                        image:
                          description: Image is the name of the image with the embedded
                            disk.
                          type: string
                        imagePullPolicy:
                          description: |-
                            Image pull policy.
                            One of Always, Never, IfNotPresent.
                            Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/containers/images#updating-images
                          type: string
                        imagePullSecret:
                          description: ImagePullSecret is the name of the Docker registry
                            secret required to pull the image. The secret must already
                            exist.
                          type: string
                        path:
                          description: Path defines the path to disk file in the container
                          type: string
                      required:
                      - image
                      type: object
                    dataVolume:
                      description: |-
                        DataVolume represents the dynamic creation a PVC for this volume as well as
                        the process of populating that PVC with a disk image.
                      properties:
                        hotpluggable:
                          description: Hotpluggable indicates whether the volume can
                            be hotplugged and hotunplugged.
                          type: boolean
                        name:
                          description: |-
                            Name of both the DataVolume and the PVC in the same namespace.
                            After PVC population the DataVolume is garbage collected by default.
                          type: string
                      required:
                      - name
                      type: object
                    downwardAPI:
                      description: DownwardAPI represents downward API about the pod
                        that should populate this volume
                      properties:
                        fields:
                          description: Fields is a list of downward API volume file
                          items:
                            description: DownwardAPIVolumeFile represents information
                              to create the file containing the pod field
                            properties:
                              fieldRef:
                                description: 'Required: Selects a field of the pod:
                                  only annotations, labels, name, namespace and uid
                                  are supported.'
                                properties:
                                  apiVersion:
                                    description: Version of the schema the FieldPath
                                      is written in terms of, defaults to "v1".
                                    type: string
                                  fieldPath:
                                    description: Path of the field to select in the
                                      specified API version.
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              mode:
                                description: |-
                                  Optional: mode bits used to set permissions on this file, must be an octal value
                                  between 0000 and 0777 or a decimal value between 0 and 511.
                                  YAML accepts both octal and decimal values, JSON requires decimal values for mode bits.
                                  If not specified, the volume defaultMode will be used.
                                  This might be in conflict with other options that affect the file
                                  mode, like fsGroup, and the result can be other mode bits set.
                                format: int32
                                type: integer
                              path:
                                description: 'Required: Path is  the relative path
                                  name of the file to be created. Must not be absolute
                                  or contain the ''..'' path. Must be utf-8 encoded.
                                  The first item of the relative path must not start
                                  with ''..'''
                                type: string
                              resourceFieldRef:
                                description: |-
                                  Selects a resource of the container: only resources limits and requests
                                  (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.
                                properties:
                                  containerName:
                                    description: 'Container name: required for volumes,
                                      optional for env vars'
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: Specifies the output format of the
                                      exposed resources, defaults to "1"
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    description: 'Required: resource to select'
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                            required:
                            - path
                            type: object
                          type: array
                        volumeLabel:
                          description: |-
                            The volume label of the resulting disk inside the VMI.
                            Different bootstrapping mechanisms require different values.
                            Typical values are "cidata" (cloud-init), "config-2" (cloud-init) or "OEMDRV" (kickstart).
                          type: string
                      type: object
                    downwardMetrics:
                      description: |-
                        DownwardMetrics adds a very small disk to VMIs which contains a limited view of host and guest
                        metrics. The disk content is compatible with vhostmd (https://github.com/vhostmd/vhostmd) and vm-dump-metrics.
                      type: object
                    emptyDisk:
                      description: |-
                        EmptyDisk represents a temporary disk which shares the vmis lifecycle.
                        More info: https://kubevirt.gitbooks.io/user-guide/disks-and-volumes.html
                      properties:
                        capacity:
                          anyOf:
                          - type: integer
                          - type: string
                          description: Capacity of the sparse disk.
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                      required:
                      - capacity
                      type: object
                    ephemeral:
                      description: Ephemeral is a special volume source that "wraps"
                        specified source and provides copy-on-write image on top of
                        it.
                      properties:
                        persistentVolumeClaim:
                          description: |-
                            PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace.
                            Directly attached to the vmi via qemu.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          properties:
                            claimName:
                              description: |-
                                claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
                                More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                              type: string
                            readOnly:
                              description: |-
                                readOnly Will force the ReadOnly setting in VolumeMounts.
                                Default false.
                              type: boolean
                          required:
                          - claimName
                          type: object
                      type: object
                    hostDisk:
                      description: HostDisk represents a disk created on the cluster
                        level
                      properties:
                        capacity:
                          anyOf:
                          - type: integer
                          - type: string
                          description: Capacity of the sparse disk
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        path:
                          description: The path to HostDisk image located on the cluster
                          type: string
                        shared:
                          description: Shared indicate whether the path is shared
                            between nodes
                          type: boolean
                        type:
                          description: |-
                            Contains information if disk.img exists or should be created
                            allowed options are 'Disk' and 'DiskOrCreate'
                          type: string
                      required:
                      - path
                      - type
                      type: object
                    memoryDump:
                      description: MemoryDump is attached to the virt launcher and
                        is populated with a memory dump of the vmi
                      properties:
                        claimName:
                          description: |-
                            claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          type: string
                        hotpluggable:
                          description: Hotpluggable indicates whether the volume can
                            be hotplugged and hotunplugged.
                          type: boolean
                        readOnly:
                          description: |-
                            readOnly Will force the ReadOnly setting in VolumeMounts.
                            Default false.
                          type: boolean
                      required:
                      - claimName
                      type: object
                    name:
                      description: |-
                        Volume's name.
                        Must be a DNS_LABEL and unique within the vmi.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      type: string
                    persistentVolumeClaim:
                      description: |-
                        PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace.
                        Directly attached to the vmi via qemu.
                        More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                      properties:
                        claimName:
                          description: |-
                            claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          type: string
                        hotpluggable:
                          description: Hotpluggable indicates whether the volume can
                            be hotplugged and hotunplugged.
                          type: boolean
                        readOnly:
                          description: |-
                            readOnly Will force the ReadOnly setting in VolumeMounts.
                            Default false.
                          type: boolean
                      required:
                      - claimName
                      type: object
                    secret:
                      description: |-
                        SecretVolumeSource represents a reference to a secret data in the same namespace.
                        More info: https://kubernetes.io/docs/concepts/configuration/secret/
                      properties:
                        optional:
                          description: Specify whether the Secret or it's keys must
                            be defined
                          type: boolean
                        secretName:
                          description: |-
                            Name of the secret in the pod's namespace to use.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#secret
                          type: string
                        volumeLabel:
                          description: |-
                            The volume label of the resulting disk inside the VMI.
                            Different bootstrapping mechanisms require different values.
                            Typical values are "cidata" (cloud-init), "config-2" (cloud-init) or "OEMDRV" (kickstart).
                          type: string
                      type: object
                    serviceAccount:
                      description: |-
                        ServiceAccountVolumeSource represents a reference to a service account.
                        There can only be one volume of this type!
                        More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
                      properties:
                        serviceAccountName:
                          description: |-
                            Name of the service account in the pod's namespace to use.
                            More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
                          type: string
                      type: object
                    sysprep:
                      description: Represents a Sysprep volume source.
                      properties:
                        configMap:
                          description: ConfigMap references a ConfigMap that contains
                            Sysprep answer file named autounattend.xml that should
                            be attached as disk of CDROM type.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        secret:
                          description: Secret references a k8s Secret that contains
                            Sysprep answer file named autounattend.xml that should
                            be attached as disk of CDROM type.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                  required:
                  - name
                  type: object
                maxItems: 256
                type: array
            required:
            - domain
            type: object
          status:
            description: Status is the high level overview of how the VirtualMachineInstance
              is doing. It contains information available to controllers and users.
            properties:
              VSOCKCID:
                description: VSOCKCID is used to track the allocated VSOCK CID in
                  the VM.
                format: int32
                type: integer
              activePods:
                additionalProperties:
                  type: string
                description: |-
                  ActivePods is a mapping of pod UID to node name.
                  It is possible for multiple pods to be running for a single VMI during migration.
                type: object
              conditions:
                description: Conditions are specific points in VirtualMachineInstance's
                  pod runtime.
                items:
                  properties:
                    lastProbeTime:
                      format: date-time
                      nullable: true
                      type: string
                    lastTransitionTime:
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              currentCPUTopology:
                description: |-
                  CurrentCPUTopology specifies the current CPU topology used by the VM workload.
                  Current topology may differ from the desired topology in the spec while CPU hotplug
                  takes place.
                properties:
                  cores:
                    description: |-
                      Cores specifies the number of cores inside the vmi.
                      Must be a value greater or equal 1.
                    format: int32
                    type: integer
                  sockets:
                    description: |-
                      Sockets specifies the number of sockets inside the vmi.
                      Must be a value greater or equal 1.
                    format: int32
                    type: integer
                  threads:
                    description: |-
                      Threads specifies the number of threads inside the vmi.
                      Must be a value greater or equal 1.
                    format: int32
                    type: integer
                type: object
              evacuationNodeName:
                description: |-
                  EvacuationNodeName is used to track the eviction process of a VMI. It stores the name of the node that we want
                  to evacuate. It is meant to be used by KubeVirt core components only and can't be set or modified by users.
                type: string
              fsFreezeStatus:
                description: |-
                  FSFreezeStatus is the state of the fs of the guest
                  it can be either frozen or thawed
                type: string
              guestOSInfo:
                description: Guest OS Information
                properties:
                  id:
                    description: Guest OS Id
                    type: string
                  kernelRelease:
                    description: Guest OS Kernel Release
                    type: string
                  kernelVersion:
                    description: Kernel version of the Guest OS
                    type: string
                  machine:
                    description: Machine type of the Guest OS
                    type: string
                  name:
                    description: Name of the Guest OS
                    type: string
                  prettyName:
                    description: Guest OS Pretty Name
                    type: string
                  version:
                    description: Guest OS Version
                    type: string
                  versionId:
                    description: Version ID of the Guest OS
                    type: string
                type: object
              interfaces:
                description: Interfaces represent the details of available network
                  interfaces.
                items:
                  properties:
                    infoSource:
                      description: 'Specifies the origin of the interface data collected.
                        values: domain, guest-agent, multus-status.'
                      type: string
                    interfaceName:
                      description: The interface name inside the Virtual Machine
                      type: string
                    ipAddress:
                      description: |-
                        IP address of a Virtual Machine interface. It is always the first item of
                        IPs
                      type: string
                    ipAddresses:
                      description: List of all IP addresses of a Virtual Machine interface
                      items:
                        type: string
                      type: array
                    mac:
                      description: Hardware address of a Virtual Machine interface
                      type: string
                    name:
                      description: Name of the interface, corresponds to name of the
                        network assigned to the interface
                      type: string
                    podInterfaceName:
                      description: PodInterfaceName represents the name of the pod
                        network interface
                      type: string
                    queueCount:
                      description: Specifies how many queues are allocated by MultiQueue
                      format: int32
                      type: integer
                  type: object
                type: array
              kernelBootStatus:
                description: KernelBootStatus contains info about the kernelBootContainer
                properties:
                  initrdInfo:
                    description: InitrdInfo show info about the initrd file
                    properties:
                      checksum:
                        description: Checksum is the checksum of the initrd file
                        format: int32
                        type: integer
                    type: object
                  kernelInfo:
                    description: KernelInfo show info about the kernel image
                    properties:
                      checksum:
                        description: Checksum is the checksum of the kernel image
                        format: int32
                        type: integer
                    type: object
                type: object
              launcherContainerImageVersion:
                description: LauncherContainerImageVersion indicates what container
                  image is currently active for the vmi.
                type: string
              machine:
                description: |-
                  Machine shows the final resulting qemu machine type. This can be different
                  than the machine type selected in the spec, due to qemus machine type alias mechanism.
                properties:
                  type:
                    description: QEMU machine type is the actual chipset of the VirtualMachineInstance.
                    type: string
                type: object
              memory:
                description: Memory shows various informations about the VirtualMachine
                  memory.
                properties:
                  guestAtBoot:
                    anyOf:
                    - type: integer
                    - type: string
                    description: GuestAtBoot specifies with how much memory the VirtualMachine
                      intiallly booted with.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  guestCurrent:
                    anyOf:
                    - type: integer
                    - type: string
                    description: GuestCurrent specifies how much memory is currently
                      available for the VirtualMachine.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  guestRequested:
                    anyOf:
                    - type: integer
                    - type: string
                    description: GuestRequested specifies how much memory was requested
                      (hotplug) for the VirtualMachine.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              migratedVolumes:
                description: MigratedVolumes lists the source and destination volumes
                  during the volume migration
                items:
                  description: StorageMigratedVolumeInfo tracks the information about
                    the source and destination volumes during the volume migration
                  properties:
                    destinationPVCInfo:
                      description: DestinationPVCInfo contains the information about
                        the destination PVC
                      properties:
                        accessModes:
                          description: |-
                            AccessModes contains the desired access modes the volume should have.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Capacity represents the capacity set on the
                            corresponding PVC status
                          type: object
                        claimName:
                          description: ClaimName is the name of the PVC
                          type: string
                        filesystemOverhead:
                          description: Percentage of filesystem's size to be reserved
                            when resizing the PVC
                          pattern: ^(0(?:\.\d{1,3})?|1)$
                          type: string
                        preallocated:
                          description: Preallocated indicates if the PVC's storage
                            is preallocated or not
                          type: boolean
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests represents the resources requested
                            by the corresponding PVC spec
                          type: object
                        volumeMode:
                          description: |-
                            VolumeMode defines what type of volume is required by the claim.
                            Value of Filesystem is implied when not included in claim spec.
                          type: string
                      type: object
                    sourcePVCInfo:
                      description: SourcePVCInfo contains the information about the
                        source PVC
                      properties:
                        accessModes:
                          description: |-
                            AccessModes contains the desired access modes the volume should have.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Capacity represents the capacity set on the
                            corresponding PVC status
                          type: object
                        claimName:
                          description: ClaimName is the name of the PVC
                          type: string
                        filesystemOverhead:
                          description: Percentage of filesystem's size to be reserved
                            when resizing the PVC
                          pattern: ^(0(?:\.\d{1,3})?|1)$
                          type: string
                        preallocated:
                          description: Preallocated indicates if the PVC's storage
                            is preallocated or not
                          type: boolean
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests represents the resources requested
                            by the corresponding PVC spec
                          type: object
                        volumeMode:
                          description: |-
                            VolumeMode defines what type of volume is required by the claim.
                            Value of Filesystem is implied when not included in claim spec.
                          type: string
                      type: object
                    volumeName:
                      description: VolumeName is the name of the volume that is being
                        migrated
                      type: string
                  required:
                  - volumeName
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              migrationMethod:
                description: 'Represents the method using which the vmi can be migrated:
                  live migration or block migration'
                type: string
              migrationState:
                description: Represents the status of a live migration
                properties:
                  abortRequested:
                    description: Indicates that the migration has been requested to
                      abort
                    type: boolean
                  abortStatus:
                    description: Indicates the final status of the live migration
                      abortion
                    type: string
                  completed:
                    description: Indicates the migration completed
                    type: boolean
                  endTimestamp:
                    description: The time the migration action ended
                    format: date-time
                    nullable: true
                    type: string
                  failed:
                    description: Indicates that the migration failed
                    type: boolean
                  failureReason:
                    description: Contains the reason why the migration failed
                    type: string
                  migrationConfiguration:
                    description: Migration configurations to apply
                    properties:
                      allowAutoConverge:
                        description: |-
                          AllowAutoConverge allows the platform to compromise performance/availability of VMIs to
                          guarantee successful VMI live migrations. Defaults to false
                        type: boolean
                      allowPostCopy:
                        description: |-
                          AllowPostCopy enables post-copy live migrations. Such migrations allow even the busiest VMIs
                          to successfully live-migrate. However, events like a network failure can cause a VMI crash.
                          If set to true, migrations will still start in pre-copy, but switch to post-copy when
                          CompletionTimeoutPerGiB triggers. Defaults to false
                        type: boolean
                      bandwidthPerMigration:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          BandwidthPerMigration limits the amount of network bandwidth live migrations are allowed to use.
                          The value is in quantity per second. Defaults to 0 (no limit)
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      completionTimeoutPerGiB:
                        description: |-
                          CompletionTimeoutPerGiB is the maximum number of seconds per GiB a migration is allowed to take.
                          If a live-migration takes longer to migrate than this value multiplied by the size of the VMI,
                          the migration will be cancelled, unless AllowPostCopy is true. Defaults to 150
                        format: int64
                        type: integer
                      disableTLS:
                        description: |-
                          When set to true, DisableTLS will disable the additional layer of live migration encryption
                          provided by KubeVirt. This is usually a bad idea. Defaults to false
                        type: boolean
                      matchSELinuxLevelOnMigration:
                        description: |-
                          By default, the SELinux level of target virt-launcher pods is forced to the level of the source virt-launcher.
                          When set to true, MatchSELinuxLevelOnMigration lets the CRI auto-assign a random level to the target.
                          That will ensure the target virt-launcher doesn't share categories with another pod on the node.
                          However, migrations will fail when using RWX volumes that don't automatically deal with SELinux levels.
                        type: boolean
                      network:
                        description: |-
                          Network is the name of the CNI network to use for live migrations. By default, migrations go
                          through the pod network.
                        type: string
                      nodeDrainTaintKey:
                        description: |-
                          NodeDrainTaintKey defines the taint key that indicates a node should be drained.
                          Note: this option relies on the deprecated node taint feature. Default: kubevirt.io/drain
                        type: string
                      parallelMigrationsPerCluster:
                        description: |-
                          ParallelMigrationsPerCluster is the total number of concurrent live migrations
                          allowed cluster-wide. Defaults to 5
                        format: int32
                        type: integer
                      parallelOutboundMigrationsPerNode:
                        description: |-
                          ParallelOutboundMigrationsPerNode is the maximum number of concurrent outgoing live migrations
                          allowed per node. Defaults to 2
                        format: int32
                        type: integer
                      progressTimeout:
                        description: |-
                          ProgressTimeout is the maximum number of seconds a live migration is allowed to make no progress.
                          Hitting this timeout means a migration transferred 0 data for that many seconds. The migration is
                          then considered stuck and therefore cancelled. Defaults to 150
                        format: int64
                        type: integer
                      unsafeMigrationOverride:
                        description: |-
                          UnsafeMigrationOverride allows live migrations to occur even if the compatibility check
                          indicates the migration will be unsafe to the guest. Defaults to false
                        type: boolean
                    type: object
                  migrationPolicyName:
                    description: Name of the migration policy. If string is empty,
                      no policy is matched
                    type: string
                  migrationUid:
                    description: The VirtualMachineInstanceMigration object associated
                      with this migration
                    type: string
                  mode:
                    description: Lets us know if the vmi is currently running pre
                      or post copy migration
                    type: string
                  sourceNode:
                    description: The source node that the VMI originated on
                    type: string
                  sourcePersistentStatePVCName:
                    description: If the VMI being migrated uses persistent features
                      (backend-storage), its source PVC name is saved here
                    type: string
                  sourcePod:
                    type: string
                  startTimestamp:
                    description: The time the migration action began
                    format: date-time
                    nullable: true
                    type: string
                  targetAttachmentPodUID:
                    description: The UID of the target attachment pod for hotplug
                      volumes
                    type: string
                  targetCPUSet:
                    description: |-
                      If the VMI requires dedicated CPUs, this field will
                      hold the dedicated CPU set on the target node
                    items:
                      type: integer
                    type: array
                    x-kubernetes-list-type: atomic
                  targetDirectMigrationNodePorts:
                    additionalProperties:
                      type: integer
                    description: The list of ports opened for live migration on the
                      destination node
                    type: object
                  targetNode:
                    description: The target node that the VMI is moving to
                    type: string
                  targetNodeAddress:
                    description: The address of the target node to use for the migration
                    type: string
                  targetNodeDomainDetected:
                    description: The Target Node has seen the Domain Start Event
                    type: boolean
                  targetNodeDomainReadyTimestamp:
                    description: The timestamp at which the target node detects the
                      domain is active
                    format: date-time
                    type: string
                  targetNodeTopology:
                    description: |-
                      If the VMI requires dedicated CPUs, this field will
                      hold the numa topology on the target node
                    type: string
                  targetPersistentStatePVCName:
                    description: If the VMI being migrated uses persistent features
                      (backend-storage), its target PVC name is saved here
                    type: string
                  targetPod:
                    description: The target pod that the VMI is moving to
                    type: string
                type: object
              migrationTransport:
                description: This represents the migration transport
                type: string
              nodeName:
                description: NodeName is the name where the VirtualMachineInstance
                  is currently running.
                type: string
              phase:
                description: Phase is the status of the VirtualMachineInstance in
                  kubernetes world. It is not the VirtualMachineInstance status, but
                  partially correlates to it.
                type: string
              phaseTransitionTimestamps:
                description: PhaseTransitionTimestamp is the timestamp of when the
                  last phase change occurred
                items:
                  description: VirtualMachineInstancePhaseTransitionTimestamp gives
                    a timestamp in relation to when a phase is set on a vmi
                  properties:
                    phase:
                      description: Phase is the status of the VirtualMachineInstance
                        in kubernetes world. It is not the VirtualMachineInstance
                        status, but partially correlates to it.
                      type: string
                    phaseTransitionTimestamp:
                      description: PhaseTransitionTimestamp is the timestamp of when
                        the phase change occurred
                      format: date-time
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              qosClass:
                description: |-
                  The Quality of Service (QOS) classification assigned to the virtual machine instance based on resource requirements
                  See PodQOSClass type for available QOS classes
                  More info: https://git.k8s.io/community/contributors/design-proposals/node/resource-qos.md
                type: string
              reason:
                description: A brief CamelCase message indicating details about why
                  the VMI is in this state. e.g. 'NodeUnresponsive'
                type: string
              runtimeUser:
                description: RuntimeUser is used to determine what user will be used
                  in launcher
                format: int64
                type: integer
              selinuxContext:
                description: SELinuxContext is the actual SELinux context of the virt-launcher
                  pod
                type: string
              topologyHints:
                properties:
                  tscFrequency:
                    format: int64
                    type: integer
                type: object
              virtualMachineRevisionName:
                description: |-
                  VirtualMachineRevisionName is used to get the vm revision of the vmi when doing
                  an online vm snapshot
                type: string
              volumeStatus:
                description: VolumeStatus contains the statuses of all the volumes
                items:
                  description: VolumeStatus represents information about the status
                    of volumes attached to the VirtualMachineInstance.
                  properties:
                    containerDiskVolume:
                      description: ContainerDiskVolume shows info about the containerdisk,
                        if the volume is a containerdisk
                      properties:
                        checksum:
                          description: Checksum is the checksum of the rootdisk or
                            kernel artifacts inside the containerdisk
                          format: int32
                          type: integer
                      type: object
                    hotplugVolume:
                      description: If the volume is hotplug, this will contain the
                        hotplug status.
                      properties:
                        attachPodName:
                          description: AttachPodName is the name of the pod used to
                            attach the volume to the node.
                          type: string
                        attachPodUID:
                          description: AttachPodUID is the UID of the pod used to
                            attach the volume to the node.
                          type: string
                      type: object
                    memoryDumpVolume:
                      description: If the volume is memorydump volume, this will contain
                        the memorydump info.
                      properties:
                        claimName:
                          description: ClaimName is the name of the pvc the memory
                            was dumped to
                          type: string
                        endTimestamp:
                          description: EndTimestamp is the time when the memory dump
                            completed
                          format: date-time
                          type: string
                        startTimestamp:
                          description: StartTimestamp is the time when the memory
                            dump started
                          format: date-time
                          type: string
                        targetFileName:
                          description: TargetFileName is the name of the memory dump
                            output
                          type: string
                      type: object
                    message:
                      description: Message is a detailed message about the current
                        hotplug volume phase
                      type: string
                    name:
                      description: Name is the name of the volume
                      type: string
                    persistentVolumeClaimInfo:
                      description: PersistentVolumeClaimInfo is information about
                        the PVC that handler requires during start flow
                      properties:
                        accessModes:
                          description: |-
                            AccessModes contains the desired access modes the volume should have.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Capacity represents the capacity set on the
                            corresponding PVC status
                          type: object
                        claimName:
                          description: ClaimName is the name of the PVC
                          type: string
                        filesystemOverhead:
                          description: Percentage of filesystem's size to be reserved
                            when resizing the PVC
                          pattern: ^(0(?:\.\d{1,3})?|1)$
                          type: string
                        preallocated:
                          description: Preallocated indicates if the PVC's storage
                            is preallocated or not
                          type: boolean
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests represents the resources requested
                            by the corresponding PVC spec
                          type: object
                        volumeMode:
                          description: |-
                            VolumeMode defines what type of volume is required by the claim.
                            Value of Filesystem is implied when not included in claim spec.
                          type: string
                      type: object
                    phase:
                      description: Phase is the phase
                      type: string
                    reason:
                      description: Reason is a brief description of why we are in
                        the current hotplug volume phase
                      type: string
                    size:
                      description: Represents the size of the volume
                      format: int64
                      type: integer
                    target:
                      description: 'Target is the target name used when adding the
                        volume to the VM, eg: vda'
                      type: string
                  required:
                  - name
                  - target
                  type: object
                type: array
                x-kubernetes-list-type: atomic
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
  - additionalPrinterColumns:
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .status.interfaces[0].ipAddress
      name: IP
      type: string
    - jsonPath: .status.nodeName
      name: NodeName
      type: string
    - jsonPath: .status.conditions[?(@.type=='Ready')].status
      name: Ready
      type: string
    - jsonPath: .status.conditions[?(@.type=='LiveMigratable')].status
      name: Live-Migratable
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=='Paused')].status
      name: Paused
      priority: 1
      type: string
    deprecated: true
    deprecationWarning: kubevirt.io/v1alpha3 is now deprecated and will be removed
      in a future release.
    name: v1alpha3
    schema:
      openAPIV3Schema:
        description: VirtualMachineInstance is *the* VirtualMachineInstance Definition.
          It represents a virtual machine in the runtime environment of kubernetes.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: VirtualMachineInstance Spec contains the VirtualMachineInstance
              specification.
            properties:
              accessCredentials:
                description: Specifies a set of public keys to inject into the vm
                  guest
                items:
                  description: |-
                    AccessCredential represents a credential source that can be used to
                    authorize remote access to the vm guest
                    Only one of its members may be specified.
                  properties:
                    sshPublicKey:
                      description: |-
                        SSHPublicKey represents the source and method of applying a ssh public
                        key into a guest virtual machine.
                      properties:
                        propagationMethod:
                          description: PropagationMethod represents how the public
                            key is injected into the vm guest.
                          properties:
                            configDrive:
                              description: |-
                                ConfigDrivePropagation means that the ssh public keys are injected
                                into the VM using metadata using the configDrive cloud-init provider
                              type: object
                            noCloud:
                              description: |-
                                NoCloudPropagation means that the ssh public keys are injected
                                into the VM using metadata using the noCloud cloud-init provider
                              type: object
                            qemuGuestAgent:
                              description: |-
                                QemuGuestAgentAccessCredentailPropagation means ssh public keys are
                                dynamically injected into the vm at runtime via the qemu guest agent.
                                This feature requires the qemu guest agent to be running within the guest.
                              properties:
                                users:
                                  description: |-
                                    Users represents a list of guest users that should have the ssh public keys
                                    added to their authorized_keys file.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: set
                              required:
                              - users
                              type: object
                          type: object
                        source:
                          description: Source represents where the public keys are
                            pulled from
                          properties:
                            secret:
                              description: Secret means that the access credential
                                is pulled from a kubernetes secret
                              properties:
                                secretName:
                                  description: SecretName represents the name of the
                                    secret in the VMI's namespace
                                  type: string
                              required:
                              - secretName
                              type: object
                          type: object
                      required:
                      - propagationMethod
                      - source
                      type: object
                    userPassword:
                      description: |-
                        UserPassword represents the source and method for applying a guest user's
                        password
                      properties:
                        propagationMethod:
                          description: propagationMethod represents how the user passwords
                            are injected into the vm guest.
                          properties:
                            qemuGuestAgent:
                              description: |-
                                QemuGuestAgentAccessCredentailPropagation means passwords are
                                dynamically injected into the vm at runtime via the qemu guest agent.
                                This feature requires the qemu guest agent to be running within the guest.
                              type: object
                          type: object
                        source:
                          description: Source represents where the user passwords
                            are pulled from
                          properties:
                            secret:
                              description: Secret means that the access credential
                                is pulled from a kubernetes secret
                              properties:
                                secretName:
                                  description: SecretName represents the name of the
                                    secret in the VMI's namespace
                                  type: string
                              required:
                              - secretName
                              type: object
                          type: object
                      required:
                      - propagationMethod
                      - source
                      type: object
                  type: object
                maxItems: 256
                type: array
                x-kubernetes-list-type: atomic
              affinity:
                description: If affinity is specifies, obey all the affinity rules
                properties:
                  nodeAffinity:
                    description: Describes node affinity scheduling rules for the
                      pod.
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          The scheduler will prefer to schedule pods to nodes that satisfy
                          the affinity expressions specified by this field, but it may choose
                          a node that violates one or more of the expressions. The node that is
                          most preferred is the one with the greatest sum of weights, i.e.
                          for each node that meets all of the scheduling requirements (resource
                          request, requiredDuringScheduling affinity expressions, etc.),
                          compute a sum by iterating through the elements of this field and adding
                          "weight" to the sum if the node matches the corresponding matchExpressions; the
                          node(s) with the highest sum are the most preferred.
                        items:
                          description: |-
                            An empty preferred scheduling term matches all objects with implicit weight 0
                            (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
                          properties:
                            preference:
                              description: A node selector term, associated with the
                                corresponding weight.
                              properties:
                                matchExpressions:
                                  description: A list of node selector requirements
                                    by node's labels.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  description: A list of node selector requirements
                                    by node's fields.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            weight:
                              description: Weight associated with matching the corresponding
                                nodeSelectorTerm, in the range 1-100.
                              format: int32
                              type: integer
                          required:
                          - preference
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          If the affinity requirements specified by this field are not met at
                          scheduling time, the pod will not be scheduled onto the node.
                          If the affinity requirements specified by this field cease to be met
                          at some point during pod execution (e.g. due to an update), the system
                          may or may not try to eventually evict the pod from its node.
                        properties:
                          nodeSelectorTerms:
                            description: Required. A list of node selector terms.
                              The terms are ORed.
                            items:
                              description: |-
                                A null or empty node selector term matches no objects. The requirements of
                                them are ANDed.
                                The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
                              properties:
                                matchExpressions:
                                  description: A list of node selector requirements
                                    by node's labels.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchFields:
                                  description: A list of node selector requirements
                                    by node's fields.
                                  items:
                                    description: |-
                                      A node selector requirement is a selector that contains values, a key, and an operator
                                      that relates the key and values.
                                    properties:
                                      key:
                                        description: The label key that the selector
                                          applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          Represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                        type: string
                                      values:
                                        description: |-
                                          An array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. If the operator is Gt or Lt, the values
                                          array must have a single element, which will be interpreted as an integer.
                                          This array is replaced during a strategic merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                              x-kubernetes-map-type: atomic
                            type: array
                            x-kubernetes-list-type: atomic
                        required:
                        - nodeSelectorTerms
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  podAffinity:
                    description: Describes pod affinity scheduling rules (e.g. co-locate
                      this pod in the same node, zone, etc. as some other pod(s)).
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          The scheduler will prefer to schedule pods to nodes that satisfy
                          the affinity expressions specified by this field, but it may choose
                          a node that violates one or more of the expressions. The node that is
                          most preferred is the one with the greatest sum of weights, i.e.
                          for each node that meets all of the scheduling requirements (resource
                          request, requiredDuringScheduling affinity expressions, etc.),
                          compute a sum by iterating through the elements of this field and adding
                          "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                          node(s) with the highest sum are the most preferred.
                        items:
                          description: The weights of all of the matched WeightedPodAffinityTerm
                            fields are added per-node to find the most preferred node(s)
                          properties:
                            podAffinityTerm:
                              description: Required. A pod affinity term, associated
                                with the corresponding weight.
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              description: |-
                                weight associated with matching the corresponding podAffinityTerm,
                                in the range 1-100.
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          If the affinity requirements specified by this field are not met at
                          scheduling time, the pod will not be scheduled onto the node.
                          If the affinity requirements specified by this field cease to be met
                          at some point during pod execution (e.g. due to a pod label update), the
                          system may or may not try to eventually evict the pod from its node.
                          When there are multiple elements, the lists of nodes corresponding to each
                          podAffinityTerm are intersected, i.e. all terms must be satisfied.
                        items:
                          description: |-
                            Defines a set of pods (namely those matching the labelSelector
                            relative to the given namespace(s)) that this pod should be
                            co-located (affinity) or not co-located (anti-affinity) with,
                            where co-located is defined as running on a node whose value of
                            the label with key <topologyKey> matches that of any node on which
                            a pod of the set of pods is running
                          properties:
                            labelSelector:
                              description: |-
                                A label query over a set of resources, in this case pods.
                                If it's null, this PodAffinityTerm matches with no Pods.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              description: |-
                                MatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              description: |-
                                MismatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              description: |-
                                A label query over the set of namespaces that the term applies to.
                                The term is applied to the union of the namespaces selected by this field
                                and the ones listed in the namespaces field.
                                null selector and null or empty namespaces list means "this pod's namespace".
                                An empty selector ({}) matches all namespaces.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                namespaces specifies a static list of namespace names that the term applies to.
                                The term is applied to the union of the namespaces listed in this field
                                and the ones selected by namespaceSelector.
                                null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              description: |-
                                This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                whose value of the label with key topologyKey matches that of any node on which any of the
                                selected pods is running.
                                Empty topologyKey is not allowed.
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  podAntiAffinity:
                    description: Describes pod anti-affinity scheduling rules (e.g.
                      avoid putting this pod in the same node, zone, etc. as some
                      other pod(s)).
                    properties:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          The scheduler will prefer to schedule pods to nodes that satisfy
                          the anti-affinity expressions specified by this field, but it may choose
                          a node that violates one or more of the expressions. The node that is
                          most preferred is the one with the greatest sum of weights, i.e.
                          for each node that meets all of the scheduling requirements (resource
                          request, requiredDuringScheduling anti-affinity expressions, etc.),
                          compute a sum by iterating through the elements of this field and adding
                          "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                          node(s) with the highest sum are the most preferred.
                        items:
                          description: The weights of all of the matched WeightedPodAffinityTerm
                            fields are added per-node to find the most preferred node(s)
                          properties:
                            podAffinityTerm:
                              description: Required. A pod affinity term, associated
                                with the corresponding weight.
                              properties:
                                labelSelector:
                                  description: |-
                                    A label query over a set of resources, in this case pods.
                                    If it's null, this PodAffinityTerm matches with no Pods.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                matchLabelKeys:
                                  description: |-
                                    MatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                    Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                mismatchLabelKeys:
                                  description: |-
                                    MismatchLabelKeys is a set of pod label keys to select which pods will
                                    be taken into consideration. The keys are used to lookup values from the
                                    incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                    to select the group of existing pods which pods will be taken into consideration
                                    for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                    pod labels will be ignored. The default value is empty.
                                    The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                    Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                    This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                namespaceSelector:
                                  description: |-
                                    A label query over the set of namespaces that the term applies to.
                                    The term is applied to the union of the namespaces selected by this field
                                    and the ones listed in the namespaces field.
                                    null selector and null or empty namespaces list means "this pod's namespace".
                                    An empty selector ({}) matches all namespaces.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty. This array is replaced during a strategic
                                              merge patch.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: |-
                                        matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                        map is equivalent to an element of matchExpressions, whose key field is "key", the
                                        operator is "In", and the values array contains only "value". The requirements are ANDed.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                namespaces:
                                  description: |-
                                    namespaces specifies a static list of namespace names that the term applies to.
                                    The term is applied to the union of the namespaces listed in this field
                                    and the ones selected by namespaceSelector.
                                    null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                topologyKey:
                                  description: |-
                                    This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                    the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                    whose value of the label with key topologyKey matches that of any node on which any of the
                                    selected pods is running.
                                    Empty topologyKey is not allowed.
                                  type: string
                              required:
                              - topologyKey
                              type: object
                            weight:
                              description: |-
                                weight associated with matching the corresponding podAffinityTerm,
                                in the range 1-100.
                              format: int32
                              type: integer
                          required:
                          - podAffinityTerm
                          - weight
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      requiredDuringSchedulingIgnoredDuringExecution:
                        description: |-
                          If the anti-affinity requirements specified by this field are not met at
                          scheduling time, the pod will not be scheduled onto the node.
                          If the anti-affinity requirements specified by this field cease to be met
                          at some point during pod execution (e.g. due to a pod label update), the
                          system may or may not try to eventually evict the pod from its node.
                          When there are multiple elements, the lists of nodes corresponding to each
                          podAffinityTerm are intersected, i.e. all terms must be satisfied.
                        items:
                          description: |-
                            Defines a set of pods (namely those matching the labelSelector
                            relative to the given namespace(s)) that this pod should be
                            co-located (affinity) or not co-located (anti-affinity) with,
                            where co-located is defined as running on a node whose value of
                            the label with key <topologyKey> matches that of any node on which
                            a pod of the set of pods is running
                          properties:
                            labelSelector:
                              description: |-
                                A label query over a set of resources, in this case pods.
                                If it's null, this PodAffinityTerm matches with no Pods.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            matchLabelKeys:
                              description: |-
                                MatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key in (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            mismatchLabelKeys:
                              description: |-
                                MismatchLabelKeys is a set of pod label keys to select which pods will
                                be taken into consideration. The keys are used to lookup values from the
                                incoming pod labels, those key-value labels are merged with 'labelSelector' as 'key notin (value)'
                                to select the group of existing pods which pods will be taken into consideration
                                for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                pod labels will be ignored. The default value is empty.
                                The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            namespaceSelector:
                              description: |-
                                A label query over the set of namespaces that the term applies to.
                                The term is applied to the union of the namespaces selected by this field
                                and the ones listed in the namespaces field.
                                null selector and null or empty namespaces list means "this pod's namespace".
                                An empty selector ({}) matches all namespaces.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            namespaces:
                              description: |-
                                namespaces specifies a static list of namespace names that the term applies to.
                                The term is applied to the union of the namespaces listed in this field
                                and the ones selected by namespaceSelector.
                                null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            topologyKey:
                              description: |-
                                This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                whose value of the label with key topologyKey matches that of any node on which any of the
                                selected pods is running.
                                Empty topologyKey is not allowed.
                              type: string
                          required:
                          - topologyKey
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                type: object
              architecture:
                description: Specifies the architecture of the vm guest you are attempting
                  to run. Defaults to the compiled architecture of the KubeVirt components
                type: string
              dnsConfig:
                description: |-
                  Specifies the DNS parameters of a pod.
                  Parameters specified here will be merged to the generated DNS
                  configuration based on DNSPolicy.
                properties:
                  nameservers:
                    description: |-
                      A list of DNS name server IP addresses.
                      This will be appended to the base nameservers generated from DNSPolicy.
                      Duplicated nameservers will be removed.
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                  options:
                    description: |-
                      A list of DNS resolver options.
                      This will be merged with the base options generated from DNSPolicy.
                      Duplicated entries will be removed. Resolution options given in Options
                      will override those that appear in the base DNSPolicy.
                    items:
                      description: PodDNSConfigOption defines DNS resolver options
                        of a pod.
                      properties:
                        name:
                          description: Required.
                          type: string
                        value:
                          type: string
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  searches:
                    description: |-
                      A list of DNS search domains for host-name lookup.
                      This will be appended to the base search paths generated from DNSPolicy.
                      Duplicated search paths will be removed.
                    items:
                      type: string
                    type: array
                    x-kubernetes-list-type: atomic
                type: object
              dnsPolicy:
                description: |-
                  Set DNS policy for the pod.
                  Defaults to "ClusterFirst".
                  Valid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'.
                  DNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy.
                  To have DNS options set along with hostNetwork, you have to specify DNS policy
                  explicitly to 'ClusterFirstWithHostNet'.
                type: string
              domain:
                description: Specification of the desired behavior of the VirtualMachineInstance
                  on the host.
                properties:
                  chassis:
                    description: Chassis specifies the chassis info passed to the
                      domain.
                    properties:
                      asset:
                        type: string
                      manufacturer:
                        type: string
                      serial:
                        type: string
                      sku:
                        type: string
                      version:
                        type: string
                    type: object
                  clock:
                    description: Clock sets the clock and timers of the vmi.
                    properties:
                      timer:
                        description: Timer specifies whih timers are attached to the
                          vmi.
                        properties:
                          hpet:
                            description: HPET (High Precision Event Timer) - multiple
                              timers with periodic interrupts.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                              tickPolicy:
                                description: |-
                                  TickPolicy determines what happens when QEMU misses a deadline for injecting a tick to the guest.
                                  One of "delay", "catchup", "merge", "discard".
                                type: string
                            type: object
                          hyperv:
                            description: Hyperv (Hypervclock) - lets guests read the
                              host’s wall clock time (paravirtualized). For windows
                              guests.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                            type: object
                          kvm:
                            description: "KVM \t(KVM clock) - lets guests read the
                              host’s wall clock time (paravirtualized). For linux
                              guests."
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                            type: object
                          pit:
                            description: PIT (Programmable Interval Timer) - a timer
                              with periodic interrupts.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                              tickPolicy:
                                description: |-
                                  TickPolicy determines what happens when QEMU misses a deadline for injecting a tick to the guest.
                                  One of "delay", "catchup", "discard".
                                type: string
                            type: object
                          rtc:
                            description: RTC (Real Time Clock) - a continuously running
                              timer with periodic interrupts.
                            properties:
                              present:
                                description: |-
                                  Enabled set to false makes sure that the machine type or a preset can't add the timer.
                                  Defaults to true.
                                type: boolean
                              tickPolicy:
                                description: |-
                                  TickPolicy determines what happens when QEMU misses a deadline for injecting a tick to the guest.
                                  One of "delay", "catchup".
                                type: string
                              track:
                                description: Track the guest or the wall clock.
                                type: string
                            type: object
                        type: object
                      timezone:
                        description: |-
                          Timezone sets the guest clock to the specified timezone.
                          Zone name follows the TZ environment variable format (e.g. 'America/New_York').
                        type: string
                      utc:
                        description: |-
                          UTC sets the guest clock to UTC on each boot. If an offset is specified,
                          guest changes to the clock will be kept during reboots and are not reset.
                        properties:
                          offsetSeconds:
                            description: |-
                              OffsetSeconds specifies an offset in seconds, relative to UTC. If set,
                              guest changes to the clock will be kept during reboots and not reset.
                            type: integer
                        type: object
                    type: object
                    x-kubernetes-preserve-unknown-fields: true
                  cpu:
                    description: CPU allow specified the detailed CPU topology inside
                      the vmi.
                    properties:
                      cores:
                        description: |-
                          Cores specifies the number of cores inside the vmi.
                          Must be a value greater or equal 1.
                        format: int32
                        type: integer
                      dedicatedCpuPlacement:
                        description: |-
                          DedicatedCPUPlacement requests the scheduler to place the VirtualMachineInstance on a node
                          with enough dedicated pCPUs and pin the vCPUs to it.
                        type: boolean
                      features:
                        description: Features specifies the CPU features list inside
                          the VMI.
                        items:
                          description: CPUFeature allows specifying a CPU feature.
                          properties:
                            name:
                              description: Name of the CPU feature
                              type: string
                            policy:
                              description: |-
                                Policy is the CPU feature attribute which can have the following attributes:
                                force    - The virtual CPU will claim the feature is supported regardless of it being supported by host CPU.
                                require  - Guest creation will fail unless the feature is supported by the host CPU or the hypervisor is able to emulate it.
                                optional - The feature will be supported by virtual CPU if and only if it is supported by host CPU.
                                disable  - The feature will not be supported by virtual CPU.
                                forbid   - Guest creation will fail if the feature is supported by host CPU.
                                Defaults to require
                              type: string
                          required:
                          - name
                          type: object
                        type: array
                      isolateEmulatorThread:
                        description: |-
                          IsolateEmulatorThread requests one more dedicated pCPU to be allocated for the VMI to place
                          the emulator thread on it.
                        type: boolean
                      maxSockets:
                        description: |-
                          MaxSockets specifies the maximum amount of sockets that can
                          be hotplugged
                        format: int32
                        type: integer
                      model:
                        description: |-
                          Model specifies the CPU model inside the VMI.
                          List of available models https://github.com/libvirt/libvirt/tree/master/src/cpu_map.
                          It is possible to specify special cases like "host-passthrough" to get the same CPU as the node
                          and "host-model" to get CPU closest to the node one.
                          Defaults to host-model.
                        type: string
                      numa:
                        description: NUMA allows specifying settings for the guest
                          NUMA topology
                        properties:
                          guestMappingPassthrough:
                            description: |-
                              GuestMappingPassthrough will create an efficient guest topology based on host CPUs exclusively assigned to a pod.
                              The created topology ensures that memory and CPUs on the virtual numa nodes never cross boundaries of host numa nodes.
                            type: object
                        type: object
                      realtime:
                        description: Realtime instructs the virt-launcher to tune
                          the VMI for lower latency, optional for real time workloads
                        properties:
                          mask:
                            description: |-
                              Mask defines the vcpu mask expression that defines which vcpus are used for realtime. Format matches libvirt's expressions.
                              Example: "0-3,^1","0,2,3","2-3"
                            type: string
                        type: object
                      sockets:
                        description: |-
                          Sockets specifies the number of sockets inside the vmi.
                          Must be a value greater or equal 1.
                        format: int32
                        type: integer
                      threads:
                        description: |-
                          Threads specifies the number of threads inside the vmi.
                          Must be a value greater or equal 1.
                        format: int32
                        type: integer
                    type: object
                  devices:
                    description: Devices allows adding disks, network interfaces,
                      and others
                    properties:
                      autoattachGraphicsDevice:
                        description: |-
                          Whether to attach the default graphics device or not.
                          VNC will not be available if set to false. Defaults to true.
                        type: boolean
                      autoattachInputDevice:
                        description: |-
                          Whether to attach an Input Device.
                          Defaults to false.
                        type: boolean
                      autoattachMemBalloon:
                        description: |-
                          Whether to attach the Memory balloon device with default period.
                          Period can be adjusted in virt-config.
                          Defaults to true.
                        type: boolean
                      autoattachPodInterface:
                        description: Whether to attach a pod network interface. Defaults
                          to true.
                        type: boolean
                      autoattachSerialConsole:
                        description: |-
                          Whether to attach the default virtio-serial console or not.
                          Serial console access will not be available if set to false. Defaults to true.
                        type: boolean
                      autoattachVSOCK:
                        description: |-
                          Whether to attach the VSOCK CID to the VM or not.
                          VSOCK access will be available if set to true. Defaults to false.
                        type: boolean
                      blockMultiQueue:
                        description: |-
                          Whether or not to enable virtio multi-queue for block devices.
                          Defaults to false.
                        type: boolean
                      clientPassthrough:
                        description: To configure and access client devices such as
                          redirecting USB
                        type: object
                      disableHotplug:
                        description: DisableHotplug disabled the ability to hotplug
                          disks.
                        type: boolean
                      disks:
                        description: Disks describes disks, cdroms and luns which
                          are connected to the vmi.
                        items:
                          properties:
                            blockSize:
                              description: If specified, the virtual disk will be
                                presented with the given block sizes.
                              properties:
                                custom:
                                  description: CustomBlockSize represents the desired
                                    logical and physical block size for a VM disk.
                                  properties:
                                    logical:
                                      type: integer
                                    physical:
                                      type: integer
                                  required:
                                  - logical
                                  - physical
                                  type: object
                                matchVolume:
                                  description: Represents if a feature is enabled
                                    or disabled.
                                  properties:
                                    enabled:
                                      description: |-
                                        Enabled determines if the feature should be enabled or disabled on the guest.
                                        Defaults to true.
                                      type: boolean
                                  type: object
                              type: object
                            bootOrder:
                              description: |-
                                BootOrder is an integer value > 0, used to determine ordering of boot devices.
                                Lower values take precedence.
                                Each disk or interface that has a boot order must have a unique value.
                                Disks without a boot order are not tried if a disk with a boot order exists.
                              type: integer
                            cache:
                              description: |-
                                Cache specifies which kvm disk cache mode should be used.
                                Supported values are: CacheNone, CacheWriteThrough.
                              type: string
                            cdrom:
                              description: Attach a volume as a cdrom to the vmi.
                              properties:
                                bus:
                                  description: |-
                                    Bus indicates the type of disk device to emulate.
                                    supported values: virtio, sata, scsi.
                                  type: string
                                readonly:
                                  description: |-
                                    ReadOnly.
                                    Defaults to true.
                                  type: boolean
                                tray:
                                  description: |-
                                    Tray indicates if the tray of the device is open or closed.
                                    Allowed values are "open" and "closed".
                                    Defaults to closed.
                                  type: string
                              type: object
                            dedicatedIOThread:
                              description: |-
                                dedicatedIOThread indicates this disk should have an exclusive IO Thread.
                                Enabling this implies useIOThreads = true.
                                Defaults to false.
                              type: boolean
                            disk:
                              description: Attach a volume as a disk to the vmi.
                              properties:
                                bus:
                                  description: |-
                                    Bus indicates the type of disk device to emulate.
                                    supported values: virtio, sata, scsi, usb.
                                  type: string
                                pciAddress:
                                  description: 'If specified, the virtual disk will
                                    be placed on the guests pci address with the specified
                                    PCI address. For example: 0000:81:01.10'
                                  type: string
                                readonly:
                                  description: |-
                                    ReadOnly.
                                    Defaults to false.
                                  type: boolean
                              type: object
                            errorPolicy:
                              description: If specified, it can change the default
                                error policy (stop) for the disk
                              type: string
                            io:
                              description: |-
                                IO specifies which QEMU disk IO mode should be used.
                                Supported values are: native, default, threads.
                              type: string
                            lun:
                              description: Attach a volume as a LUN to the vmi.
                              properties:
                                bus:
                                  description: |-
                                    Bus indicates the type of disk device to emulate.
                                    supported values: virtio, sata, scsi.
                                  type: string
                                readonly:
                                  description: |-
                                    ReadOnly.
                                    Defaults to false.
                                  type: boolean
                                reservation:
                                  description: Reservation indicates if the disk needs
                                    to support the persistent reservation for the
                                    SCSI disk
                                  type: boolean
                              type: object
                            name:
                              description: Name is the device name
                              type: string
                            serial:
                              description: Serial provides the ability to specify
                                a serial number for the disk device.
                              type: string
                            shareable:
                              description: If specified the disk is made sharable
                                and multiple write from different VMs are permitted
                              type: boolean
                            tag:
                              description: If specified, disk address and its tag
                                will be provided to the guest via config drive metadata
                              type: string
                          required:
                          - name
                          type: object
                        maxItems: 256
                        type: array
                      downwardMetrics:
                        description: DownwardMetrics creates a virtio serials for
                          exposing the downward metrics to the vmi.
                        type: object
                      filesystems:
                        description: Filesystems describes filesystem which is connected
                          to the vmi.
                        items:
                          properties:
                            name:
                              description: Name is the device name
                              type: string
                            virtiofs:
                              description: Virtiofs is supported
                              type: object
                          required:
                          - name
                          - virtiofs
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      gpus:
                        description: Whether to attach a GPU device to the vmi.
                        items:
                          properties:
                            deviceName:
                              type: string
                            name:
                              description: Name of the GPU device as exposed by a
                                device plugin
                              type: string
                            tag:
                              description: If specified, the virtual network interface
                                address and its tag will be provided to the guest
                                via config drive
                              type: string
                            virtualGPUOptions:
                              properties:
                                display:
                                  properties:
                                    enabled:
                                      description: |-
                                        Enabled determines if a display addapter backed by a vGPU should be enabled or disabled on the guest.
                                        Defaults to true.
                                      type: boolean
                                    ramFB:
                                      description: |-
                                        Enables a boot framebuffer, until the guest OS loads a real GPU driver
                                        Defaults to true.
                                      properties:
                                        enabled:
                                          description: |-
                                            Enabled determines if the feature should be enabled or disabled on the guest.
                                            Defaults to true.
                                          type: boolean
                                      type: object
                                  type: object
                              type: object
                          required:
                          - deviceName
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      hostDevices:
                        description: Whether to attach a host device to the vmi.
                        items:
                          properties:
                            deviceName:
                              description: DeviceName is the resource name of the
                                host device exposed by a device plugin
                              type: string
                            name:
                              type: string
                            tag:
                              description: If specified, the virtual network interface
                                address and its tag will be provided to the guest
                                via config drive
                              type: string
                          required:
                          - deviceName
                          - name
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      inputs:
                        description: Inputs describe input devices
                        items:
                          properties:
                            bus:
                              description: |-
                                Bus indicates the bus of input device to emulate.
                                Supported values: virtio, usb.
                              type: string
                            name:
                              description: Name is the device name
                              type: string
                            type:
                              description: |-
                                Type indicated the type of input device.
                                Supported values: tablet.
                              type: string
                          required:
                          - name
                          - type
                          type: object
                        type: array
                      interfaces:
                        description: Interfaces describe network interfaces which
                          are added to the vmi.
                        items:
                          properties:
                            acpiIndex:
                              description: |-
                                If specified, the ACPI index is used to provide network interface device naming, that is stable across changes
                                in PCI addresses assigned to the device.
                                This value is required to be unique across all devices and be between 1 and (16*1024-1).
                              type: integer
                            binding:
                              description: |-
                                Binding specifies the binding plugin that will be used to connect the interface to the guest.
                                It provides an alternative to InterfaceBindingMethod.
                                version: 1alphav1
                              properties:
                                name:
                                  description: |-
                                    Name references to the binding name as denined in the kubevirt CR.
                                    version: 1alphav1
                                  type: string
                              required:
                              - name
                              type: object
                            bootOrder:
                              description: |-
                                BootOrder is an integer value > 0, used to determine ordering of boot devices.
                                Lower values take precedence.
                                Each interface or disk that has a boot order must have a unique value.
                                Interfaces without a boot order are not tried.
                              type: integer
                            bridge:
                              description: InterfaceBridge connects to a given network
                                via a linux bridge.
                              type: object
                            dhcpOptions:
                              description: If specified the network interface will
                                pass additional DHCP options to the VMI
                              properties:
                                bootFileName:
                                  description: If specified will pass option 67 to
                                    interface's DHCP server
                                  type: string
                                ntpServers:
                                  description: If specified will pass the configured
                                    NTP server to the VM via DHCP option 042.
                                  items:
                                    type: string
                                  type: array
                                privateOptions:
                                  description: 'If specified will pass extra DHCP
                                    options for private use, range: 224-254'
                                  items:
                                    description: DHCPExtraOptions defines Extra DHCP
                                      options for a VM.
                                    properties:
                                      option:
                                        description: |-
                                          Option is an Integer value from 224-254
                                          Required.
                                        type: integer
                                      value:
                                        description: |-
                                          Value is a String value for the Option provided
                                          Required.
                                        type: string
                                    required:
                                    - option
                                    - value
                                    type: object
                                  type: array
                                tftpServerName:
                                  description: If specified will pass option 66 to
                                    interface's DHCP server
                                  type: string
                              type: object
                            macAddress:
                              description: 'Interface MAC address. For example: de:ad:00:00:be:af
                                or DE-AD-00-00-BE-AF.'
                              type: string
                            macvtap:
                              description: |-
                                DeprecatedMacvtap is an alias to the deprecated Macvtap interface,
                                please refer to Kubevirt user guide for alternatives.
                                Deprecated: Removed in v1.3
                              type: object
                            masquerade:
                              description: InterfaceMasquerade connects to a given
                                network using netfilter rules to nat the traffic.
                              type: object
                            model:
                              description: |-
                                Interface model.
                                One of: e1000, e1000e, igb, ne2k_pci, pcnet, rtl8139, virtio.
                                Defaults to virtio.
                              type: string
                            name:
                              description: |-
                                Logical name of the interface as well as a reference to the associated networks.
                                Must match the Name of a Network.
                              type: string
                            passt:
                              description: |-
                                DeprecatedPasst is an alias to the deprecated Passt interface,
                                please refer to Kubevirt user guide for alternatives.
                                Deprecated: Removed in v1.3
                              type: object
                            pciAddress:
                              description: 'If specified, the virtual network interface
                                will be placed on the guests pci address with the
                                specified PCI address. For example: 0000:81:01.10'
                              type: string
                            ports:
                              description: List of ports to be forwarded to the virtual
                                machine.
                              items:
                                description: |-
                                  Port represents a port to expose from the virtual machine.
                                  Default protocol TCP.
                                  The port field is mandatory
                                properties:
                                  name:
                                    description: |-
                                      If specified, this must be an IANA_SVC_NAME and unique within the pod. Each
                                      named port in a pod must have a unique name. Name for the port that can be
                                      referred to by services.
                                    type: string
                                  port:
                                    description: |-
                                      Number of port to expose for the virtual machine.
                                      This must be a valid port number, 0 < x < 65536.
                                    format: int32
                                    type: integer
                                  protocol:
                                    description: |-
                                      Protocol for port. Must be UDP or TCP.
                                      Defaults to "TCP".
                                    type: string
                                required:
                                - port
                                type: object
                              type: array
                            slirp:
                              description: |-
                                DeprecatedSlirp is an alias to the deprecated Slirp interface
                                Deprecated: Removed in v1.3
                              type: object
                            sriov:
                              description: InterfaceSRIOV connects to a given network
                                by passing-through an SR-IOV PCI device via vfio.
                              type: object
                            state:
                              description: |-
                                State represents the requested operational state of the interface.
                                The (only) value supported is 'absent', expressing a request to remove the interface.
                              type: string
                            tag:
                              description: If specified, the virtual network interface
                                address and its tag will be provided to the guest
                                via config drive
                              type: string
                          required:
                          - name
                          type: object
                        maxItems: 256
                        type: array
                      logSerialConsole:
                        description: |-
                          Whether to log the auto-attached default serial console or not.
                          Serial console logs will be collect to a file and then streamed from a named 'guest-console-log'.
                          Not relevant if autoattachSerialConsole is disabled.
                          Defaults to cluster wide setting on VirtualMachineOptions.
                        type: boolean
                      networkInterfaceMultiqueue:
                        description: If specified, virtual network interfaces configured
                          with a virtio bus will also enable the vhost multiqueue
                          feature for network devices. The number of queues created
                          depends on additional factors of the VirtualMachineInstance,
                          like the number of guest CPUs.
                        type: boolean
                      rng:
                        description: Whether to have random number generator from
                          host
                        type: object
                      sound:
                        description: Whether to emulate a sound device.
                        properties:
                          model:
                            description: |-
                              We only support ich9 or ac97.
                              If SoundDevice is not set: No sound card is emulated.
                              If SoundDevice is set but Model is not: ich9
                            type: string
                          name:
                            description: User's defined name for this sound device
                            type: string
                        required:
                        - name
                        type: object
                      tpm:
                        description: Whether to emulate a TPM device.
                        properties:
                          persistent:
                            description: |-
                              Persistent indicates the state of the TPM device should be kept accross reboots
                              Defaults to false
                            type: boolean
                        type: object
                      useVirtioTransitional:
                        description: |-
                          Fall back to legacy virtio 0.9 support if virtio bus is selected on devices.
                          This is helpful for old machines like CentOS6 or RHEL6 which
                          do not understand virtio_non_transitional (virtio 1.0).
                        type: boolean
                      watchdog:
                        description: Watchdog describes a watchdog device which can
                          be added to the vmi.
                        properties:
                          i6300esb:
                            description: i6300esb watchdog device.
                            properties:
                              action:
                                description: |-
                                  The action to take. Valid values are poweroff, reset, shutdown.
                                  Defaults to reset.
                                type: string
                            type: object
                          name:
                            description: Name of the watchdog.
                            type: string
                        required:
                        - name
                        type: object
                    type: object
                  features:
                    description: Features like acpi, apic, hyperv, smm.
                    properties:
                      acpi:
                        description: |-
                          ACPI enables/disables ACPI inside the guest.
                          Defaults to enabled.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                        type: object
                      apic:
                        description: Defaults to the machine type setting.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                          endOfInterrupt:
                            description: |-
                              EndOfInterrupt enables the end of interrupt notification in the guest.
                              Defaults to false.
                            type: boolean
                        type: object
                      hyperv:
                        description: Defaults to the machine type setting.
                        properties:
                          evmcs:
                            description: |-
                              EVMCS Speeds up L2 vmexits, but disables other virtualization features. Requires vapic.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          frequencies:
                            description: |-
                              Frequencies improves the TSC clock source handling for Hyper-V on KVM.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          ipi:
                            description: |-
                              IPI improves performances in overcommited environments. Requires vpindex.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          reenlightenment:
                            description: |-
                              Reenlightenment enables the notifications on TSC frequency changes.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          relaxed:
                            description: |-
                              Relaxed instructs the guest OS to disable watchdog timeouts.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          reset:
                            description: |-
                              Reset enables Hyperv reboot/reset for the vmi. Requires synic.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          runtime:
                            description: |-
                              Runtime improves the time accounting to improve scheduling in the guest.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          spinlocks:
                            description: Spinlocks allows to configure the spinlock
                              retry attempts.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                              spinlocks:
                                description: |-
                                  Retries indicates the number of retries.
                                  Must be a value greater or equal 4096.
                                  Defaults to 4096.
                                format: int32
                                type: integer
                            type: object
                          synic:
                            description: |-
                              SyNIC enables the Synthetic Interrupt Controller.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          synictimer:
                            description: |-
                              SyNICTimer enables Synthetic Interrupt Controller Timers, reducing CPU load.
                              Defaults to the machine type setting.
                            properties:
                              direct:
                                description: Represents if a feature is enabled or
                                  disabled.
                                properties:
                                  enabled:
                                    description: |-
                                      Enabled determines if the feature should be enabled or disabled on the guest.
                                      Defaults to true.
                                    type: boolean
                                type: object
                              enabled:
                                type: boolean
                            type: object
                          tlbflush:
                            description: |-
                              TLBFlush improves performances in overcommited environments. Requires vpindex.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          vapic:
                            description: |-
                              VAPIC improves the paravirtualized handling of interrupts.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                          vendorid:
                            description: |-
                              VendorID allows setting the hypervisor vendor id.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                              vendorid:
                                description: |-
                                  VendorID sets the hypervisor vendor id, visible to the vmi.
                                  String up to twelve characters.
                                type: string
                            type: object
                          vpindex:
                            description: |-
                              VPIndex enables the Virtual Processor Index to help windows identifying virtual processors.
                              Defaults to the machine type setting.
                            properties:
                              enabled:
                                description: |-
                                  Enabled determines if the feature should be enabled or disabled on the guest.
                                  Defaults to true.
                                type: boolean
                            type: object
                        type: object
                      hypervPassthrough:
                        description: |-
                          This enables all supported hyperv flags automatically.
                          Bear in mind that if this enabled hyperV features cannot
                          be enabled explicitly. In addition, a Virtual Machine
                          using it will be non-migratable.
                        properties:
                          enabled:
                            type: boolean
                        type: object
                      kvm:
                        description: Configure how KVM presence is exposed to the
                          guest.
                        properties:
                          hidden:
                            description: |-
                              Hide the KVM hypervisor from standard MSR based discovery.
                              Defaults to false
                            type: boolean
                        type: object
                      pvspinlock:
                        description: |-
                          Notify the guest that the host supports paravirtual spinlocks.
                          For older kernels this feature should be explicitly disabled.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                        type: object
                      smm:
                        description: |-
                          SMM enables/disables System Management Mode.
                          TSEG not yet implemented.
                        properties:
                          enabled:
                            description: |-
                              Enabled determines if the feature should be enabled or disabled on the guest.
                              Defaults to true.
                            type: boolean
                        type: object
                    type: object
                  firmware:
                    description: Firmware.
                    properties:
                      acpi:
                        description: Information that can be set in the ACPI table
                        properties:
                          slicNameRef:
                            description: |-
                              SlicNameRef should match the volume name of a secret object. The data in the secret should
                              be a binary blob that follows the ACPI SLIC standard, see:
                              https://learn.microsoft.com/en-us/previous-versions/windows/hardware/design/dn653305(v=vs.85)
                            type: string
                        type: object
                      bootloader:
                        description: Settings to control the bootloader that is used.
                        properties:
                          bios:
                            description: If set (default), BIOS will be used.
                            properties:
                              useSerial:
                                description: If set, the BIOS output will be transmitted
                                  over serial
                                type: boolean
                            type: object
                          efi:
                            description: If set, EFI will be used instead of BIOS.
                            properties:
                              persistent:
                                description: |-
                                  If set to true, Persistent will persist the EFI NVRAM across reboots.
                                  Defaults to false
                                type: boolean
                              secureBoot:
                                description: |-
                                  If set, SecureBoot will be enabled and the OVMF roms will be swapped for
                                  SecureBoot-enabled ones.
                                  Requires SMM to be enabled.
                                  Defaults to true
                                type: boolean
                            type: object
                        type: object
                      kernelBoot:
                        description: Settings to set the kernel for booting.
                        properties:
                          container:
                            description: Container defines the container that containes
                              kernel artifacts
                            properties:
                              image:
                                description: Image that contains initrd / kernel files.
                                type: string
                              imagePullPolicy:
                                description: |-
                                  Image pull policy.
                                  One of Always, Never, IfNotPresent.
                                  Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
                                  Cannot be updated.
                                  More info: https://kubernetes.io/docs/concepts/containers/images#updating-images
                                type: string
                              imagePullSecret:
                                description: ImagePullSecret is the name of the Docker
                                  registry secret required to pull the image. The
                                  secret must already exist.
                                type: string
                              initrdPath:
                                description: the fully-qualified path to the ramdisk
                                  image in the host OS
                                type: string
                              kernelPath:
                                description: The fully-qualified path to the kernel
                                  image in the host OS
                                type: string
                            required:
                            - image
                            type: object
                          kernelArgs:
                            description: Arguments to be passed to the kernel at boot
                              time
                            type: string
                        type: object
                      serial:
                        description: The system-serial-number in SMBIOS
                        type: string
                      uuid:
                        description: |-
                          UUID reported by the vmi bios.
                          Defaults to a random generated uid.
                        type: string
                    type: object
                  ioThreadsPolicy:
                    description: |-
                      Controls whether or not disks will share IOThreads.
                      Omitting IOThreadsPolicy disables use of IOThreads.
                      One of: shared, auto
                    type: string
                  launchSecurity:
                    description: Launch Security setting of the vmi.
                    properties:
                      sev:
                        description: AMD Secure Encrypted Virtualization (SEV).
                        properties:
                          attestation:
                            description: If specified, run the attestation process
                              for a vmi.
                            type: object
                          dhCert:
                            description: Base64 encoded guest owner's Diffie-Hellman
                              key.
                            type: string
                          policy:
                            description: |-
                              Guest policy flags as defined in AMD SEV API specification.
                              Note: due to security reasons it is not allowed to enable guest debugging. Therefore NoDebug flag is not exposed to users and is always true.
                            properties:
                              encryptedState:
                                description: |-
                                  SEV-ES is required.
                                  Defaults to false.
                                type: boolean
                            type: object
                          session:
                            description: Base64 encoded session blob.
                            type: string
                        type: object
                    type: object
                  machine:
                    description: Machine type.
                    properties:
                      type:
                        description: QEMU machine type is the actual chipset of the
                          VirtualMachineInstance.
                        type: string
                    type: object
                  memory:
                    description: Memory allow specifying the VMI memory features.
                    properties:
                      guest:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Guest allows to specifying the amount of memory which is visible inside the Guest OS.
                          The Guest must lie between Requests and Limits from the resources section.
                          Defaults to the requested memory in the resources section if not specified.
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      hugepages:
                        description: Hugepages allow to use hugepages for the VirtualMachineInstance
                          instead of regular memory.
                        properties:
                          pageSize:
                            description: PageSize specifies the hugepage size, for
                              x86_64 architecture valid values are 1Gi and 2Mi.
                            type: string
                        type: object
                      maxGuest:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          MaxGuest allows to specify the maximum amount of memory which is visible inside the Guest OS.
                          The delta between MaxGuest and Guest is the amount of memory that can be hot(un)plugged.
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                    type: object
                  resources:
                    description: Resources describes the Compute Resources required
                      by this vmi.
                    properties:
                      limits:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Limits describes the maximum amount of compute resources allowed.
                          Valid resource keys are "memory" and "cpu".
                        type: object
                      overcommitGuestOverhead:
                        description: |-
                          Don't ask the scheduler to take the guest-management overhead into account. Instead
                          put the overhead only into the container's memory limit. This can lead to crashes if
                          all memory is in use on a node. Defaults to false.
                        type: boolean
                      requests:
                        additionalProperties:
                          anyOf:
                          - type: integer
                          - type: string
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        description: |-
                          Requests is a description of the initial vmi resources.
                          Valid resource keys are "memory" and "cpu".
                        type: object
                    type: object
                required:
                - devices
                type: object
              evictionStrategy:
                description: |-
                  EvictionStrategy describes the strategy to follow when a node drain occurs.
                  The possible options are:
                  - "None": No action will be taken, according to the specified 'RunStrategy' the VirtualMachine will be restarted or shutdown.
                  - "LiveMigrate": the VirtualMachineInstance will be migrated instead of being shutdown.
                  - "LiveMigrateIfPossible": the same as "LiveMigrate" but only if the VirtualMachine is Live-Migratable, otherwise it will behave as "None".
                  - "External": the VirtualMachineInstance will be protected by a PDB and 'vmi.Status.EvacuationNodeName' will be set on eviction. This is mainly useful for cluster-api-provider-kubevirt (capk) which needs a way for VMI's to be blocked from eviction, yet signal capk that eviction has been called on the VMI so the capk controller can handle tearing the VMI down. Details can be found in the commit description https://github.com/kubevirt/kubevirt/commit/c1d77face705c8b126696bac9a3ee3825f27f1fa.
                type: string
              hostname:
                description: |-
                  Specifies the hostname of the vmi
                  If not specified, the hostname will be set to the name of the vmi, if dhcp or cloud-init is configured properly.
                type: string
              livenessProbe:
                description: |-
                  Periodic probe of VirtualMachineInstance liveness.
                  VirtualmachineInstances will be stopped if the probe fails.
                  Cannot be updated.
                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                properties:
                  exec:
                    description: |-
                      One and only one of the following should be specified.
                      Exec specifies the action to take, it will be executed on the guest through the qemu-guest-agent.
                      If the guest agent is not available, this probe will fail.
                    properties:
                      command:
                        description: |-
                          Command is the command line to execute inside the container, the working directory for the
                          command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
                          not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
                          a shell, you need to explicitly call out to that shell.
                          Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  failureThreshold:
                    description: |-
                      Minimum consecutive failures for the probe to be considered failed after having succeeded.
                      Defaults to 3. Minimum value is 1.
                    format: int32
                    type: integer
                  guestAgentPing:
                    description: GuestAgentPing contacts the qemu-guest-agent for
                      availability checks.
                    type: object
                  httpGet:
                    description: HTTPGet specifies the http request to perform.
                    properties:
                      host:
                        description: |-
                          Host name to connect to, defaults to the pod IP. You probably want to set
                          "Host" in httpHeaders instead.
                        type: string
                      httpHeaders:
                        description: Custom headers to set in the request. HTTP allows
                          repeated headers.
                        items:
                          description: HTTPHeader describes a custom header to be
                            used in HTTP probes
                          properties:
                            name:
                              description: |-
                                The header field name.
                                This will be canonicalized upon output, so case-variant names will be understood as the same header.
                              type: string
                            value:
                              description: The header field value
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      path:
                        description: Path to access on the HTTP server.
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Name or number of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                      scheme:
                        description: |-
                          Scheme to use for connecting to the host.
                          Defaults to HTTP.
                        type: string
                    required:
                    - port
                    type: object
                  initialDelaySeconds:
                    description: |-
                      Number of seconds after the VirtualMachineInstance has started before liveness probes are initiated.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                  periodSeconds:
                    description: |-
                      How often (in seconds) to perform the probe.
                      Default to 10 seconds. Minimum value is 1.
                    format: int32
                    type: integer
                  successThreshold:
                    description: |-
                      Minimum consecutive successes for the probe to be considered successful after having failed.
                      Defaults to 1. Must be 1 for liveness. Minimum value is 1.
                    format: int32
                    type: integer
                  tcpSocket:
                    description: |-
                      TCPSocket specifies an action involving a TCP port.
                      TCP hooks not yet supported
                    properties:
                      host:
                        description: 'Optional: Host name to connect to, defaults
                          to the pod IP.'
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Number or name of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                    required:
                    - port
                    type: object
                  timeoutSeconds:
                    description: |-
                      Number of seconds after which the probe times out.
                      For exec probes the timeout fails the probe but does not terminate the command running on the guest.
                      This means a blocking command can result in an increasing load on the guest.
                      A small buffer will be added to the resulting workload exec probe to compensate for delays
                      caused by the qemu guest exec mechanism.
                      Defaults to 1 second. Minimum value is 1.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                type: object
              networks:
                description: List of networks that can be attached to a vm's virtual
                  interface.
                items:
                  description: Network represents a network type and a resource that
                    should be connected to the vm.
                  properties:
                    multus:
                      description: Represents the multus cni network.
                      properties:
                        default:
                          description: |-
                            Select the default network and add it to the
                            multus-cni.io/default-network annotation.
                          type: boolean
                        networkName:
                          description: |-
                            References to a NetworkAttachmentDefinition CRD object. Format:
                            <networkName>, <namespace>/<networkName>. If namespace is not
                            specified, VMI namespace is assumed.
                          type: string
                      required:
                      - networkName
                      type: object
                    name:
                      description: |-
                        Network name.
                        Must be a DNS_LABEL and unique within the vm.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      type: string
                    pod:
                      description: Represents the stock pod network interface.
                      properties:
                        vmIPv6NetworkCIDR:
                          description: |-
                            IPv6 CIDR for the vm network.
                            Defaults to fd10:0:2::/120 if not specified.
                          type: string
                        vmNetworkCIDR:
                          description: |-
                            CIDR for vm network.
                            Default ********/24 if not specified.
                          type: string
                      type: object
                  required:
                  - name
                  type: object
                maxItems: 256
                type: array
              nodeSelector:
                additionalProperties:
                  type: string
                description: |-
                  NodeSelector is a selector which must be true for the vmi to fit on a node.
                  Selector which must match a node's labels for the vmi to be scheduled on that node.
                  More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
                type: object
              priorityClassName:
                description: |-
                  If specified, indicates the pod's priority.
                  If not specified, the pod priority will be default or zero if there is no
                  default.
                type: string
              readinessProbe:
                description: |-
                  Periodic probe of VirtualMachineInstance service readiness.
                  VirtualmachineInstances will be removed from service endpoints if the probe fails.
                  Cannot be updated.
                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                properties:
                  exec:
                    description: |-
                      One and only one of the following should be specified.
                      Exec specifies the action to take, it will be executed on the guest through the qemu-guest-agent.
                      If the guest agent is not available, this probe will fail.
                    properties:
                      command:
                        description: |-
                          Command is the command line to execute inside the container, the working directory for the
                          command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
                          not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
                          a shell, you need to explicitly call out to that shell.
                          Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
                        items:
                          type: string
                        type: array
                        x-kubernetes-list-type: atomic
                    type: object
                  failureThreshold:
                    description: |-
                      Minimum consecutive failures for the probe to be considered failed after having succeeded.
                      Defaults to 3. Minimum value is 1.
                    format: int32
                    type: integer
                  guestAgentPing:
                    description: GuestAgentPing contacts the qemu-guest-agent for
                      availability checks.
                    type: object
                  httpGet:
                    description: HTTPGet specifies the http request to perform.
                    properties:
                      host:
                        description: |-
                          Host name to connect to, defaults to the pod IP. You probably want to set
                          "Host" in httpHeaders instead.
                        type: string
                      httpHeaders:
                        description: Custom headers to set in the request. HTTP allows
                          repeated headers.
                        items:
                          description: HTTPHeader describes a custom header to be
                            used in HTTP probes
                          properties:
                            name:
                              description: |-
                                The header field name.
                                This will be canonicalized upon output, so case-variant names will be understood as the same header.
                              type: string
                            value:
                              description: The header field value
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                        x-kubernetes-list-type: atomic
                      path:
                        description: Path to access on the HTTP server.
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Name or number of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                      scheme:
                        description: |-
                          Scheme to use for connecting to the host.
                          Defaults to HTTP.
                        type: string
                    required:
                    - port
                    type: object
                  initialDelaySeconds:
                    description: |-
                      Number of seconds after the VirtualMachineInstance has started before liveness probes are initiated.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                  periodSeconds:
                    description: |-
                      How often (in seconds) to perform the probe.
                      Default to 10 seconds. Minimum value is 1.
                    format: int32
                    type: integer
                  successThreshold:
                    description: |-
                      Minimum consecutive successes for the probe to be considered successful after having failed.
                      Defaults to 1. Must be 1 for liveness. Minimum value is 1.
                    format: int32
                    type: integer
                  tcpSocket:
                    description: |-
                      TCPSocket specifies an action involving a TCP port.
                      TCP hooks not yet supported
                    properties:
                      host:
                        description: 'Optional: Host name to connect to, defaults
                          to the pod IP.'
                        type: string
                      port:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          Number or name of the port to access on the container.
                          Number must be in the range 1 to 65535.
                          Name must be an IANA_SVC_NAME.
                        x-kubernetes-int-or-string: true
                    required:
                    - port
                    type: object
                  timeoutSeconds:
                    description: |-
                      Number of seconds after which the probe times out.
                      For exec probes the timeout fails the probe but does not terminate the command running on the guest.
                      This means a blocking command can result in an increasing load on the guest.
                      A small buffer will be added to the resulting workload exec probe to compensate for delays
                      caused by the qemu guest exec mechanism.
                      Defaults to 1 second. Minimum value is 1.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                    format: int32
                    type: integer
                type: object
              schedulerName:
                description: |-
                  If specified, the VMI will be dispatched by specified scheduler.
                  If not specified, the VMI will be dispatched by default scheduler.
                type: string
              startStrategy:
                description: StartStrategy can be set to "Paused" if Virtual Machine
                  should be started in paused state.
                type: string
              subdomain:
                description: |-
                  If specified, the fully qualified vmi hostname will be "<hostname>.<subdomain>.<pod namespace>.svc.<cluster domain>".
                  If not specified, the vmi will not have a domainname at all. The DNS entry will resolve to the vmi,
                  no matter if the vmi itself can pick up a hostname.
                type: string
              terminationGracePeriodSeconds:
                description: Grace period observed after signalling a VirtualMachineInstance
                  to stop after which the VirtualMachineInstance is force terminated.
                format: int64
                type: integer
              tolerations:
                description: If toleration is specified, obey all the toleration rules.
                items:
                  description: |-
                    The pod this Toleration is attached to tolerates any taint that matches
                    the triple <key,value,effect> using the matching operator <operator>.
                  properties:
                    effect:
                      description: |-
                        Effect indicates the taint effect to match. Empty means match all taint effects.
                        When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
                      type: string
                    key:
                      description: |-
                        Key is the taint key that the toleration applies to. Empty means match all taint keys.
                        If the key is empty, operator must be Exists; this combination means to match all values and all keys.
                      type: string
                    operator:
                      description: |-
                        Operator represents a key's relationship to the value.
                        Valid operators are Exists and Equal. Defaults to Equal.
                        Exists is equivalent to wildcard for value, so that a pod can
                        tolerate all taints of a particular category.
                      type: string
                    tolerationSeconds:
                      description: |-
                        TolerationSeconds represents the period of time the toleration (which must be
                        of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
                        it is not set, which means tolerate the taint forever (do not evict). Zero and
                        negative values will be treated as 0 (evict immediately) by the system.
                      format: int64
                      type: integer
                    value:
                      description: |-
                        Value is the taint value the toleration matches to.
                        If the operator is Exists, the value should be empty, otherwise just a regular string.
                      type: string
                  type: object
                type: array
              topologySpreadConstraints:
                description: |-
                  TopologySpreadConstraints describes how a group of VMIs will be spread across a given topology
                  domains. K8s scheduler will schedule VMI pods in a way which abides by the constraints.
                items:
                  description: TopologySpreadConstraint specifies how to spread matching
                    pods among the given topology.
                  properties:
                    labelSelector:
                      description: |-
                        LabelSelector is used to find matching pods.
                        Pods that match this label selector are counted to determine the number of pods
                        in their corresponding topology domain.
                      properties:
                        matchExpressions:
                          description: matchExpressions is a list of label selector
                            requirements. The requirements are ANDed.
                          items:
                            description: |-
                              A label selector requirement is a selector that contains values, a key, and an operator that
                              relates the key and values.
                            properties:
                              key:
                                description: key is the label key that the selector
                                  applies to.
                                type: string
                              operator:
                                description: |-
                                  operator represents a key's relationship to a set of values.
                                  Valid operators are In, NotIn, Exists and DoesNotExist.
                                type: string
                              values:
                                description: |-
                                  values is an array of string values. If the operator is In or NotIn,
                                  the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                  the values array must be empty. This array is replaced during a strategic
                                  merge patch.
                                items:
                                  type: string
                                type: array
                                x-kubernetes-list-type: atomic
                            required:
                            - key
                            - operator
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        matchLabels:
                          additionalProperties:
                            type: string
                          description: |-
                            matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                            map is equivalent to an element of matchExpressions, whose key field is "key", the
                            operator is "In", and the values array contains only "value". The requirements are ANDed.
                          type: object
                      type: object
                      x-kubernetes-map-type: atomic
                    matchLabelKeys:
                      description: |-
                        MatchLabelKeys is a set of pod label keys to select the pods over which
                        spreading will be calculated. The keys are used to lookup values from the
                        incoming pod labels, those key-value labels are ANDed with labelSelector
                        to select the group of existing pods over which spreading will be calculated
                        for the incoming pod. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector.
                        MatchLabelKeys cannot be set when LabelSelector isn't set.
                        Keys that don't exist in the incoming pod labels will
                        be ignored. A null or empty list means only match against labelSelector.

                        This is a beta field and requires the MatchLabelKeysInPodTopologySpread feature gate to be enabled (enabled by default).
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    maxSkew:
                      description: |-
                        MaxSkew describes the degree to which pods may be unevenly distributed.
                        When 'whenUnsatisfiable=DoNotSchedule', it is the maximum permitted difference
                        between the number of matching pods in the target topology and the global minimum.
                        The global minimum is the minimum number of matching pods in an eligible domain
                        or zero if the number of eligible domains is less than MinDomains.
                        For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same
                        labelSelector spread as 2/2/1:
                        In this case, the global minimum is 1.
                        | zone1 | zone2 | zone3 |
                        |  P P  |  P P  |   P   |
                        - if MaxSkew is 1, incoming pod can only be scheduled to zone3 to become 2/2/2;
                        scheduling it onto zone1(zone2) would make the ActualSkew(3-1) on zone1(zone2)
                        violate MaxSkew(1).
                        - if MaxSkew is 2, incoming pod can be scheduled onto any zone.
                        When 'whenUnsatisfiable=ScheduleAnyway', it is used to give higher precedence
                        to topologies that satisfy it.
                        It's a required field. Default value is 1 and 0 is not allowed.
                      format: int32
                      type: integer
                    minDomains:
                      description: |-
                        MinDomains indicates a minimum number of eligible domains.
                        When the number of eligible domains with matching topology keys is less than minDomains,
                        Pod Topology Spread treats "global minimum" as 0, and then the calculation of Skew is performed.
                        And when the number of eligible domains with matching topology keys equals or greater than minDomains,
                        this value has no effect on scheduling.
                        As a result, when the number of eligible domains is less than minDomains,
                        scheduler won't schedule more than maxSkew Pods to those domains.
                        If value is nil, the constraint behaves as if MinDomains is equal to 1.
                        Valid values are integers greater than 0.
                        When value is not nil, WhenUnsatisfiable must be DoNotSchedule.

                        For example, in a 3-zone cluster, MaxSkew is set to 2, MinDomains is set to 5 and pods with the same
                        labelSelector spread as 2/2/2:
                        | zone1 | zone2 | zone3 |
                        |  P P  |  P P  |  P P  |
                        The number of domains is less than 5(MinDomains), so "global minimum" is treated as 0.
                        In this situation, new pod with the same labelSelector cannot be scheduled,
                        because computed skew will be 3(3 - 0) if new Pod is scheduled to any of the three zones,
                        it will violate MaxSkew.
                      format: int32
                      type: integer
                    nodeAffinityPolicy:
                      description: |-
                        NodeAffinityPolicy indicates how we will treat Pod's nodeAffinity/nodeSelector
                        when calculating pod topology spread skew. Options are:
                        - Honor: only nodes matching nodeAffinity/nodeSelector are included in the calculations.
                        - Ignore: nodeAffinity/nodeSelector are ignored. All nodes are included in the calculations.

                        If this value is nil, the behavior is equivalent to the Honor policy.
                        This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.
                      type: string
                    nodeTaintsPolicy:
                      description: |-
                        NodeTaintsPolicy indicates how we will treat node taints when calculating
                        pod topology spread skew. Options are:
                        - Honor: nodes without taints, along with tainted nodes for which the incoming pod
                        has a toleration, are included.
                        - Ignore: node taints are ignored. All nodes are included.

                        If this value is nil, the behavior is equivalent to the Ignore policy.
                        This is a beta-level feature default enabled by the NodeInclusionPolicyInPodTopologySpread feature flag.
                      type: string
                    topologyKey:
                      description: |-
                        TopologyKey is the key of node labels. Nodes that have a label with this key
                        and identical values are considered to be in the same topology.
                        We consider each <key, value> as a "bucket", and try to put balanced number
                        of pods into each bucket.
                        We define a domain as a particular instance of a topology.
                        Also, we define an eligible domain as a domain whose nodes meet the requirements of
                        nodeAffinityPolicy and nodeTaintsPolicy.
                        e.g. If TopologyKey is "kubernetes.io/hostname", each Node is a domain of that topology.
                        And, if TopologyKey is "topology.kubernetes.io/zone", each zone is a domain of that topology.
                        It's a required field.
                      type: string
                    whenUnsatisfiable:
                      description: |-
                        WhenUnsatisfiable indicates how to deal with a pod if it doesn't satisfy
                        the spread constraint.
                        - DoNotSchedule (default) tells the scheduler not to schedule it.
                        - ScheduleAnyway tells the scheduler to schedule the pod in any location,
                          but giving higher precedence to topologies that would help reduce the
                          skew.
                        A constraint is considered "Unsatisfiable" for an incoming pod
                        if and only if every possible node assignment for that pod would violate
                        "MaxSkew" on some topology.
                        For example, in a 3-zone cluster, MaxSkew is set to 1, and pods with the same
                        labelSelector spread as 3/1/1:
                        | zone1 | zone2 | zone3 |
                        | P P P |   P   |   P   |
                        If WhenUnsatisfiable is set to DoNotSchedule, incoming pod can only be scheduled
                        to zone2(zone3) to become 3/2/1(3/1/2) as ActualSkew(2-1) on zone2(zone3) satisfies
                        MaxSkew(1). In other words, the cluster can still be imbalanced, but scheduler
                        won't make it *more* imbalanced.
                        It's a required field.
                      type: string
                  required:
                  - maxSkew
                  - topologyKey
                  - whenUnsatisfiable
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - topologyKey
                - whenUnsatisfiable
                x-kubernetes-list-type: map
              volumes:
                description: List of volumes that can be mounted by disks belonging
                  to the vmi.
                items:
                  description: Volume represents a named volume in a vmi.
                  properties:
                    cloudInitConfigDrive:
                      description: |-
                        CloudInitConfigDrive represents a cloud-init Config Drive user-data source.
                        The Config Drive data will be added as a disk to the vmi. A proper cloud-init installation is required inside the guest.
                        More info: https://cloudinit.readthedocs.io/en/latest/topics/datasources/configdrive.html
                      properties:
                        networkData:
                          description: NetworkData contains config drive inline cloud-init
                            networkdata.
                          type: string
                        networkDataBase64:
                          description: NetworkDataBase64 contains config drive cloud-init
                            networkdata as a base64 encoded string.
                          type: string
                        networkDataSecretRef:
                          description: NetworkDataSecretRef references a k8s secret
                            that contains config drive networkdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        secretRef:
                          description: UserDataSecretRef references a k8s secret that
                            contains config drive userdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        userData:
                          description: UserData contains config drive inline cloud-init
                            userdata.
                          type: string
                        userDataBase64:
                          description: UserDataBase64 contains config drive cloud-init
                            userdata as a base64 encoded string.
                          type: string
                      type: object
                    cloudInitNoCloud:
                      description: |-
                        CloudInitNoCloud represents a cloud-init NoCloud user-data source.
                        The NoCloud data will be added as a disk to the vmi. A proper cloud-init installation is required inside the guest.
                        More info: http://cloudinit.readthedocs.io/en/latest/topics/datasources/nocloud.html
                      properties:
                        networkData:
                          description: NetworkData contains NoCloud inline cloud-init
                            networkdata.
                          type: string
                        networkDataBase64:
                          description: NetworkDataBase64 contains NoCloud cloud-init
                            networkdata as a base64 encoded string.
                          type: string
                        networkDataSecretRef:
                          description: NetworkDataSecretRef references a k8s secret
                            that contains NoCloud networkdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        secretRef:
                          description: UserDataSecretRef references a k8s secret that
                            contains NoCloud userdata.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        userData:
                          description: UserData contains NoCloud inline cloud-init
                            userdata.
                          type: string
                        userDataBase64:
                          description: UserDataBase64 contains NoCloud cloud-init
                            userdata as a base64 encoded string.
                          type: string
                      type: object
                    configMap:
                      description: |-
                        ConfigMapSource represents a reference to a ConfigMap in the same namespace.
                        More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-pod-configmap/
                      properties:
                        name:
                          default: ""
                          description: |-
                            Name of the referent.
                            This field is effectively required, but due to backwards compatibility is
                            allowed to be empty. Instances of this type with an empty value here are
                            almost certainly wrong.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                          type: string
                        optional:
                          description: Specify whether the ConfigMap or it's keys
                            must be defined
                          type: boolean
                        volumeLabel:
                          description: |-
                            The volume label of the resulting disk inside the VMI.
                            Different bootstrapping mechanisms require different values.
                            Typical values are "cidata" (cloud-init), "config-2" (cloud-init) or "OEMDRV" (kickstart).
                          type: string
                      type: object
                      x-kubernetes-map-type: atomic
                    containerDisk:
                      description: |-
                        ContainerDisk references a docker image, embedding a qcow or raw disk.
                        More info: https://kubevirt.gitbooks.io/user-guide/registry-disk.html
                      properties:
                        image:
                          description: Image is the name of the image with the embedded
                            disk.
                          type: string
                        imagePullPolicy:
                          description: |-
                            Image pull policy.
                            One of Always, Never, IfNotPresent.
                            Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/containers/images#updating-images
                          type: string
                        imagePullSecret:
                          description: ImagePullSecret is the name of the Docker registry
                            secret required to pull the image. The secret must already
                            exist.
                          type: string
                        path:
                          description: Path defines the path to disk file in the container
                          type: string
                      required:
                      - image
                      type: object
                    dataVolume:
                      description: |-
                        DataVolume represents the dynamic creation a PVC for this volume as well as
                        the process of populating that PVC with a disk image.
                      properties:
                        hotpluggable:
                          description: Hotpluggable indicates whether the volume can
                            be hotplugged and hotunplugged.
                          type: boolean
                        name:
                          description: |-
                            Name of both the DataVolume and the PVC in the same namespace.
                            After PVC population the DataVolume is garbage collected by default.
                          type: string
                      required:
                      - name
                      type: object
                    downwardAPI:
                      description: DownwardAPI represents downward API about the pod
                        that should populate this volume
                      properties:
                        fields:
                          description: Fields is a list of downward API volume file
                          items:
                            description: DownwardAPIVolumeFile represents information
                              to create the file containing the pod field
                            properties:
                              fieldRef:
                                description: 'Required: Selects a field of the pod:
                                  only annotations, labels, name, namespace and uid
                                  are supported.'
                                properties:
                                  apiVersion:
                                    description: Version of the schema the FieldPath
                                      is written in terms of, defaults to "v1".
                                    type: string
                                  fieldPath:
                                    description: Path of the field to select in the
                                      specified API version.
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              mode:
                                description: |-
                                  Optional: mode bits used to set permissions on this file, must be an octal value
                                  between 0000 and 0777 or a decimal value between 0 and 511.
                                  YAML accepts both octal and decimal values, JSON requires decimal values for mode bits.
                                  If not specified, the volume defaultMode will be used.
                                  This might be in conflict with other options that affect the file
                                  mode, like fsGroup, and the result can be other mode bits set.
                                format: int32
                                type: integer
                              path:
                                description: 'Required: Path is  the relative path
                                  name of the file to be created. Must not be absolute
                                  or contain the ''..'' path. Must be utf-8 encoded.
                                  The first item of the relative path must not start
                                  with ''..'''
                                type: string
                              resourceFieldRef:
                                description: |-
                                  Selects a resource of the container: only resources limits and requests
                                  (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.
                                properties:
                                  containerName:
                                    description: 'Container name: required for volumes,
                                      optional for env vars'
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: Specifies the output format of the
                                      exposed resources, defaults to "1"
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    description: 'Required: resource to select'
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                            required:
                            - path
                            type: object
                          type: array
                        volumeLabel:
                          description: |-
                            The volume label of the resulting disk inside the VMI.
                            Different bootstrapping mechanisms require different values.
                            Typical values are "cidata" (cloud-init), "config-2" (cloud-init) or "OEMDRV" (kickstart).
                          type: string
                      type: object
                    downwardMetrics:
                      description: |-
                        DownwardMetrics adds a very small disk to VMIs which contains a limited view of host and guest
                        metrics. The disk content is compatible with vhostmd (https://github.com/vhostmd/vhostmd) and vm-dump-metrics.
                      type: object
                    emptyDisk:
                      description: |-
                        EmptyDisk represents a temporary disk which shares the vmis lifecycle.
                        More info: https://kubevirt.gitbooks.io/user-guide/disks-and-volumes.html
                      properties:
                        capacity:
                          anyOf:
                          - type: integer
                          - type: string
                          description: Capacity of the sparse disk.
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                      required:
                      - capacity
                      type: object
                    ephemeral:
                      description: Ephemeral is a special volume source that "wraps"
                        specified source and provides copy-on-write image on top of
                        it.
                      properties:
                        persistentVolumeClaim:
                          description: |-
                            PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace.
                            Directly attached to the vmi via qemu.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          properties:
                            claimName:
                              description: |-
                                claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
                                More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                              type: string
                            readOnly:
                              description: |-
                                readOnly Will force the ReadOnly setting in VolumeMounts.
                                Default false.
                              type: boolean
                          required:
                          - claimName
                          type: object
                      type: object
                    hostDisk:
                      description: HostDisk represents a disk created on the cluster
                        level
                      properties:
                        capacity:
                          anyOf:
                          - type: integer
                          - type: string
                          description: Capacity of the sparse disk
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                        path:
                          description: The path to HostDisk image located on the cluster
                          type: string
                        shared:
                          description: Shared indicate whether the path is shared
                            between nodes
                          type: boolean
                        type:
                          description: |-
                            Contains information if disk.img exists or should be created
                            allowed options are 'Disk' and 'DiskOrCreate'
                          type: string
                      required:
                      - path
                      - type
                      type: object
                    memoryDump:
                      description: MemoryDump is attached to the virt launcher and
                        is populated with a memory dump of the vmi
                      properties:
                        claimName:
                          description: |-
                            claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          type: string
                        hotpluggable:
                          description: Hotpluggable indicates whether the volume can
                            be hotplugged and hotunplugged.
                          type: boolean
                        readOnly:
                          description: |-
                            readOnly Will force the ReadOnly setting in VolumeMounts.
                            Default false.
                          type: boolean
                      required:
                      - claimName
                      type: object
                    name:
                      description: |-
                        Volume's name.
                        Must be a DNS_LABEL and unique within the vmi.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      type: string
                    persistentVolumeClaim:
                      description: |-
                        PersistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace.
                        Directly attached to the vmi via qemu.
                        More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                      properties:
                        claimName:
                          description: |-
                            claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          type: string
                        hotpluggable:
                          description: Hotpluggable indicates whether the volume can
                            be hotplugged and hotunplugged.
                          type: boolean
                        readOnly:
                          description: |-
                            readOnly Will force the ReadOnly setting in VolumeMounts.
                            Default false.
                          type: boolean
                      required:
                      - claimName
                      type: object
                    secret:
                      description: |-
                        SecretVolumeSource represents a reference to a secret data in the same namespace.
                        More info: https://kubernetes.io/docs/concepts/configuration/secret/
                      properties:
                        optional:
                          description: Specify whether the Secret or it's keys must
                            be defined
                          type: boolean
                        secretName:
                          description: |-
                            Name of the secret in the pod's namespace to use.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#secret
                          type: string
                        volumeLabel:
                          description: |-
                            The volume label of the resulting disk inside the VMI.
                            Different bootstrapping mechanisms require different values.
                            Typical values are "cidata" (cloud-init), "config-2" (cloud-init) or "OEMDRV" (kickstart).
                          type: string
                      type: object
                    serviceAccount:
                      description: |-
                        ServiceAccountVolumeSource represents a reference to a service account.
                        There can only be one volume of this type!
                        More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
                      properties:
                        serviceAccountName:
                          description: |-
                            Name of the service account in the pod's namespace to use.
                            More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
                          type: string
                      type: object
                    sysprep:
                      description: Represents a Sysprep volume source.
                      properties:
                        configMap:
                          description: ConfigMap references a ConfigMap that contains
                            Sysprep answer file named autounattend.xml that should
                            be attached as disk of CDROM type.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        secret:
                          description: Secret references a k8s Secret that contains
                            Sysprep answer file named autounattend.xml that should
                            be attached as disk of CDROM type.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                  required:
                  - name
                  type: object
                maxItems: 256
                type: array
            required:
            - domain
            type: object
          status:
            description: Status is the high level overview of how the VirtualMachineInstance
              is doing. It contains information available to controllers and users.
            properties:
              VSOCKCID:
                description: VSOCKCID is used to track the allocated VSOCK CID in
                  the VM.
                format: int32
                type: integer
              activePods:
                additionalProperties:
                  type: string
                description: |-
                  ActivePods is a mapping of pod UID to node name.
                  It is possible for multiple pods to be running for a single VMI during migration.
                type: object
              conditions:
                description: Conditions are specific points in VirtualMachineInstance's
                  pod runtime.
                items:
                  properties:
                    lastProbeTime:
                      format: date-time
                      nullable: true
                      type: string
                    lastTransitionTime:
                      format: date-time
                      nullable: true
                      type: string
                    message:
                      type: string
                    reason:
                      type: string
                    status:
                      type: string
                    type:
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              currentCPUTopology:
                description: |-
                  CurrentCPUTopology specifies the current CPU topology used by the VM workload.
                  Current topology may differ from the desired topology in the spec while CPU hotplug
                  takes place.
                properties:
                  cores:
                    description: |-
                      Cores specifies the number of cores inside the vmi.
                      Must be a value greater or equal 1.
                    format: int32
                    type: integer
                  sockets:
                    description: |-
                      Sockets specifies the number of sockets inside the vmi.
                      Must be a value greater or equal 1.
                    format: int32
                    type: integer
                  threads:
                    description: |-
                      Threads specifies the number of threads inside the vmi.
                      Must be a value greater or equal 1.
                    format: int32
                    type: integer
                type: object
              evacuationNodeName:
                description: |-
                  EvacuationNodeName is used to track the eviction process of a VMI. It stores the name of the node that we want
                  to evacuate. It is meant to be used by KubeVirt core components only and can't be set or modified by users.
                type: string
              fsFreezeStatus:
                description: |-
                  FSFreezeStatus is the state of the fs of the guest
                  it can be either frozen or thawed
                type: string
              guestOSInfo:
                description: Guest OS Information
                properties:
                  id:
                    description: Guest OS Id
                    type: string
                  kernelRelease:
                    description: Guest OS Kernel Release
                    type: string
                  kernelVersion:
                    description: Kernel version of the Guest OS
                    type: string
                  machine:
                    description: Machine type of the Guest OS
                    type: string
                  name:
                    description: Name of the Guest OS
                    type: string
                  prettyName:
                    description: Guest OS Pretty Name
                    type: string
                  version:
                    description: Guest OS Version
                    type: string
                  versionId:
                    description: Version ID of the Guest OS
                    type: string
                type: object
              interfaces:
                description: Interfaces represent the details of available network
                  interfaces.
                items:
                  properties:
                    infoSource:
                      description: 'Specifies the origin of the interface data collected.
                        values: domain, guest-agent, multus-status.'
                      type: string
                    interfaceName:
                      description: The interface name inside the Virtual Machine
                      type: string
                    ipAddress:
                      description: |-
                        IP address of a Virtual Machine interface. It is always the first item of
                        IPs
                      type: string
                    ipAddresses:
                      description: List of all IP addresses of a Virtual Machine interface
                      items:
                        type: string
                      type: array
                    mac:
                      description: Hardware address of a Virtual Machine interface
                      type: string
                    name:
                      description: Name of the interface, corresponds to name of the
                        network assigned to the interface
                      type: string
                    podInterfaceName:
                      description: PodInterfaceName represents the name of the pod
                        network interface
                      type: string
                    queueCount:
                      description: Specifies how many queues are allocated by MultiQueue
                      format: int32
                      type: integer
                  type: object
                type: array
              kernelBootStatus:
                description: KernelBootStatus contains info about the kernelBootContainer
                properties:
                  initrdInfo:
                    description: InitrdInfo show info about the initrd file
                    properties:
                      checksum:
                        description: Checksum is the checksum of the initrd file
                        format: int32
                        type: integer
                    type: object
                  kernelInfo:
                    description: KernelInfo show info about the kernel image
                    properties:
                      checksum:
                        description: Checksum is the checksum of the kernel image
                        format: int32
                        type: integer
                    type: object
                type: object
              launcherContainerImageVersion:
                description: LauncherContainerImageVersion indicates what container
                  image is currently active for the vmi.
                type: string
              machine:
                description: |-
                  Machine shows the final resulting qemu machine type. This can be different
                  than the machine type selected in the spec, due to qemus machine type alias mechanism.
                properties:
                  type:
                    description: QEMU machine type is the actual chipset of the VirtualMachineInstance.
                    type: string
                type: object
              memory:
                description: Memory shows various informations about the VirtualMachine
                  memory.
                properties:
                  guestAtBoot:
                    anyOf:
                    - type: integer
                    - type: string
                    description: GuestAtBoot specifies with how much memory the VirtualMachine
                      intiallly booted with.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  guestCurrent:
                    anyOf:
                    - type: integer
                    - type: string
                    description: GuestCurrent specifies how much memory is currently
                      available for the VirtualMachine.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  guestRequested:
                    anyOf:
                    - type: integer
                    - type: string
                    description: GuestRequested specifies how much memory was requested
                      (hotplug) for the VirtualMachine.
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                type: object
              migratedVolumes:
                description: MigratedVolumes lists the source and destination volumes
                  during the volume migration
                items:
                  description: StorageMigratedVolumeInfo tracks the information about
                    the source and destination volumes during the volume migration
                  properties:
                    destinationPVCInfo:
                      description: DestinationPVCInfo contains the information about
                        the destination PVC
                      properties:
                        accessModes:
                          description: |-
                            AccessModes contains the desired access modes the volume should have.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Capacity represents the capacity set on the
                            corresponding PVC status
                          type: object
                        claimName:
                          description: ClaimName is the name of the PVC
                          type: string
                        filesystemOverhead:
                          description: Percentage of filesystem's size to be reserved
                            when resizing the PVC
                          pattern: ^(0(?:\.\d{1,3})?|1)$
                          type: string
                        preallocated:
                          description: Preallocated indicates if the PVC's storage
                            is preallocated or not
                          type: boolean
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests represents the resources requested
                            by the corresponding PVC spec
                          type: object
                        volumeMode:
                          description: |-
                            VolumeMode defines what type of volume is required by the claim.
                            Value of Filesystem is implied when not included in claim spec.
                          type: string
                      type: object
                    sourcePVCInfo:
                      description: SourcePVCInfo contains the information about the
                        source PVC
                      properties:
                        accessModes:
                          description: |-
                            AccessModes contains the desired access modes the volume should have.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Capacity represents the capacity set on the
                            corresponding PVC status
                          type: object
                        claimName:
                          description: ClaimName is the name of the PVC
                          type: string
                        filesystemOverhead:
                          description: Percentage of filesystem's size to be reserved
                            when resizing the PVC
                          pattern: ^(0(?:\.\d{1,3})?|1)$
                          type: string
                        preallocated:
                          description: Preallocated indicates if the PVC's storage
                            is preallocated or not
                          type: boolean
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests represents the resources requested
                            by the corresponding PVC spec
                          type: object
                        volumeMode:
                          description: |-
                            VolumeMode defines what type of volume is required by the claim.
                            Value of Filesystem is implied when not included in claim spec.
                          type: string
                      type: object
                    volumeName:
                      description: VolumeName is the name of the volume that is being
                        migrated
                      type: string
                  required:
                  - volumeName
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              migrationMethod:
                description: 'Represents the method using which the vmi can be migrated:
                  live migration or block migration'
                type: string
              migrationState:
                description: Represents the status of a live migration
                properties:
                  abortRequested:
                    description: Indicates that the migration has been requested to
                      abort
                    type: boolean
                  abortStatus:
                    description: Indicates the final status of the live migration
                      abortion
                    type: string
                  completed:
                    description: Indicates the migration completed
                    type: boolean
                  endTimestamp:
                    description: The time the migration action ended
                    format: date-time
                    nullable: true
                    type: string
                  failed:
                    description: Indicates that the migration failed
                    type: boolean
                  failureReason:
                    description: Contains the reason why the migration failed
                    type: string
                  migrationConfiguration:
                    description: Migration configurations to apply
                    properties:
                      allowAutoConverge:
                        description: |-
                          AllowAutoConverge allows the platform to compromise performance/availability of VMIs to
                          guarantee successful VMI live migrations. Defaults to false
                        type: boolean
                      allowPostCopy:
                        description: |-
                          AllowPostCopy enables post-copy live migrations. Such migrations allow even the busiest VMIs
                          to successfully live-migrate. However, events like a network failure can cause a VMI crash.
                          If set to true, migrations will still start in pre-copy, but switch to post-copy when
                          CompletionTimeoutPerGiB triggers. Defaults to false
                        type: boolean
                      bandwidthPerMigration:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          BandwidthPerMigration limits the amount of network bandwidth live migrations are allowed to use.
                          The value is in quantity per second. Defaults to 0 (no limit)
                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                        x-kubernetes-int-or-string: true
                      completionTimeoutPerGiB:
                        description: |-
                          CompletionTimeoutPerGiB is the maximum number of seconds per GiB a migration is allowed to take.
                          If a live-migration takes longer to migrate than this value multiplied by the size of the VMI,
                          the migration will be cancelled, unless AllowPostCopy is true. Defaults to 150
                        format: int64
                        type: integer
                      disableTLS:
                        description: |-
                          When set to true, DisableTLS will disable the additional layer of live migration encryption
                          provided by KubeVirt. This is usually a bad idea. Defaults to false
                        type: boolean
                      matchSELinuxLevelOnMigration:
                        description: |-
                          By default, the SELinux level of target virt-launcher pods is forced to the level of the source virt-launcher.
                          When set to true, MatchSELinuxLevelOnMigration lets the CRI auto-assign a random level to the target.
                          That will ensure the target virt-launcher doesn't share categories with another pod on the node.
                          However, migrations will fail when using RWX volumes that don't automatically deal with SELinux levels.
                        type: boolean
                      network:
                        description: |-
                          Network is the name of the CNI network to use for live migrations. By default, migrations go
                          through the pod network.
                        type: string
                      nodeDrainTaintKey:
                        description: |-
                          NodeDrainTaintKey defines the taint key that indicates a node should be drained.
                          Note: this option relies on the deprecated node taint feature. Default: kubevirt.io/drain
                        type: string
                      parallelMigrationsPerCluster:
                        description: |-
                          ParallelMigrationsPerCluster is the total number of concurrent live migrations
                          allowed cluster-wide. Defaults to 5
                        format: int32
                        type: integer
                      parallelOutboundMigrationsPerNode:
                        description: |-
                          ParallelOutboundMigrationsPerNode is the maximum number of concurrent outgoing live migrations
                          allowed per node. Defaults to 2
                        format: int32
                        type: integer
                      progressTimeout:
                        description: |-
                          ProgressTimeout is the maximum number of seconds a live migration is allowed to make no progress.
                          Hitting this timeout means a migration transferred 0 data for that many seconds. The migration is
                          then considered stuck and therefore cancelled. Defaults to 150
                        format: int64
                        type: integer
                      unsafeMigrationOverride:
                        description: |-
                          UnsafeMigrationOverride allows live migrations to occur even if the compatibility check
                          indicates the migration will be unsafe to the guest. Defaults to false
                        type: boolean
                    type: object
                  migrationPolicyName:
                    description: Name of the migration policy. If string is empty,
                      no policy is matched
                    type: string
                  migrationUid:
                    description: The VirtualMachineInstanceMigration object associated
                      with this migration
                    type: string
                  mode:
                    description: Lets us know if the vmi is currently running pre
                      or post copy migration
                    type: string
                  sourceNode:
                    description: The source node that the VMI originated on
                    type: string
                  sourcePersistentStatePVCName:
                    description: If the VMI being migrated uses persistent features
                      (backend-storage), its source PVC name is saved here
                    type: string
                  sourcePod:
                    type: string
                  startTimestamp:
                    description: The time the migration action began
                    format: date-time
                    nullable: true
                    type: string
                  targetAttachmentPodUID:
                    description: The UID of the target attachment pod for hotplug
                      volumes
                    type: string
                  targetCPUSet:
                    description: |-
                      If the VMI requires dedicated CPUs, this field will
                      hold the dedicated CPU set on the target node
                    items:
                      type: integer
                    type: array
                    x-kubernetes-list-type: atomic
                  targetDirectMigrationNodePorts:
                    additionalProperties:
                      type: integer
                    description: The list of ports opened for live migration on the
                      destination node
                    type: object
                  targetNode:
                    description: The target node that the VMI is moving to
                    type: string
                  targetNodeAddress:
                    description: The address of the target node to use for the migration
                    type: string
                  targetNodeDomainDetected:
                    description: The Target Node has seen the Domain Start Event
                    type: boolean
                  targetNodeDomainReadyTimestamp:
                    description: The timestamp at which the target node detects the
                      domain is active
                    format: date-time
                    type: string
                  targetNodeTopology:
                    description: |-
                      If the VMI requires dedicated CPUs, this field will
                      hold the numa topology on the target node
                    type: string
                  targetPersistentStatePVCName:
                    description: If the VMI being migrated uses persistent features
                      (backend-storage), its target PVC name is saved here
                    type: string
                  targetPod:
                    description: The target pod that the VMI is moving to
                    type: string
                type: object
              migrationTransport:
                description: This represents the migration transport
                type: string
              nodeName:
                description: NodeName is the name where the VirtualMachineInstance
                  is currently running.
                type: string
              phase:
                description: Phase is the status of the VirtualMachineInstance in
                  kubernetes world. It is not the VirtualMachineInstance status, but
                  partially correlates to it.
                type: string
              phaseTransitionTimestamps:
                description: PhaseTransitionTimestamp is the timestamp of when the
                  last phase change occurred
                items:
                  description: VirtualMachineInstancePhaseTransitionTimestamp gives
                    a timestamp in relation to when a phase is set on a vmi
                  properties:
                    phase:
                      description: Phase is the status of the VirtualMachineInstance
                        in kubernetes world. It is not the VirtualMachineInstance
                        status, but partially correlates to it.
                      type: string
                    phaseTransitionTimestamp:
                      description: PhaseTransitionTimestamp is the timestamp of when
                        the phase change occurred
                      format: date-time
                      type: string
                  type: object
                type: array
                x-kubernetes-list-type: atomic
              qosClass:
                description: |-
                  The Quality of Service (QOS) classification assigned to the virtual machine instance based on resource requirements
                  See PodQOSClass type for available QOS classes
                  More info: https://git.k8s.io/community/contributors/design-proposals/node/resource-qos.md
                type: string
              reason:
                description: A brief CamelCase message indicating details about why
                  the VMI is in this state. e.g. 'NodeUnresponsive'
                type: string
              runtimeUser:
                description: RuntimeUser is used to determine what user will be used
                  in launcher
                format: int64
                type: integer
              selinuxContext:
                description: SELinuxContext is the actual SELinux context of the virt-launcher
                  pod
                type: string
              topologyHints:
                properties:
                  tscFrequency:
                    format: int64
                    type: integer
                type: object
              virtualMachineRevisionName:
                description: |-
                  VirtualMachineRevisionName is used to get the vm revision of the vmi when doing
                  an online vm snapshot
                type: string
              volumeStatus:
                description: VolumeStatus contains the statuses of all the volumes
                items:
                  description: VolumeStatus represents information about the status
                    of volumes attached to the VirtualMachineInstance.
                  properties:
                    containerDiskVolume:
                      description: ContainerDiskVolume shows info about the containerdisk,
                        if the volume is a containerdisk
                      properties:
                        checksum:
                          description: Checksum is the checksum of the rootdisk or
                            kernel artifacts inside the containerdisk
                          format: int32
                          type: integer
                      type: object
                    hotplugVolume:
                      description: If the volume is hotplug, this will contain the
                        hotplug status.
                      properties:
                        attachPodName:
                          description: AttachPodName is the name of the pod used to
                            attach the volume to the node.
                          type: string
                        attachPodUID:
                          description: AttachPodUID is the UID of the pod used to
                            attach the volume to the node.
                          type: string
                      type: object
                    memoryDumpVolume:
                      description: If the volume is memorydump volume, this will contain
                        the memorydump info.
                      properties:
                        claimName:
                          description: ClaimName is the name of the pvc the memory
                            was dumped to
                          type: string
                        endTimestamp:
                          description: EndTimestamp is the time when the memory dump
                            completed
                          format: date-time
                          type: string
                        startTimestamp:
                          description: StartTimestamp is the time when the memory
                            dump started
                          format: date-time
                          type: string
                        targetFileName:
                          description: TargetFileName is the name of the memory dump
                            output
                          type: string
                      type: object
                    message:
                      description: Message is a detailed message about the current
                        hotplug volume phase
                      type: string
                    name:
                      description: Name is the name of the volume
                      type: string
                    persistentVolumeClaimInfo:
                      description: PersistentVolumeClaimInfo is information about
                        the PVC that handler requires during start flow
                      properties:
                        accessModes:
                          description: |-
                            AccessModes contains the desired access modes the volume should have.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        capacity:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Capacity represents the capacity set on the
                            corresponding PVC status
                          type: object
                        claimName:
                          description: ClaimName is the name of the PVC
                          type: string
                        filesystemOverhead:
                          description: Percentage of filesystem's size to be reserved
                            when resizing the PVC
                          pattern: ^(0(?:\.\d{1,3})?|1)$
                          type: string
                        preallocated:
                          description: Preallocated indicates if the PVC's storage
                            is preallocated or not
                          type: boolean
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests represents the resources requested
                            by the corresponding PVC spec
                          type: object
                        volumeMode:
                          description: |-
                            VolumeMode defines what type of volume is required by the claim.
                            Value of Filesystem is implied when not included in claim spec.
                          type: string
                      type: object
                    phase:
                      description: Phase is the phase
                      type: string
                    reason:
                      description: Reason is a brief description of why we are in
                        the current hotplug volume phase
                      type: string
                    size:
                      description: Represents the size of the volume
                      format: int64
                      type: integer
                    target:
                      description: 'Target is the target name used when adding the
                        volume to the VM, eg: vda'
                      type: string
                  required:
                  - name
                  - target
                  type: object
                type: array
                x-kubernetes-list-type: atomic
            type: object
        required:
        - spec
        type: object
    served: true
    storage: false
status:
  acceptedNames:
    categories:
    - all
    kind: VirtualMachineInstance
    listKind: VirtualMachineInstanceList
    plural: virtualmachineinstances
    shortNames:
    - vmi
    - vmis
    singular: virtualmachineinstance
  conditions:
  - lastTransitionTime: "2025-05-15T10:08:53Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-05-15T10:08:53Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1

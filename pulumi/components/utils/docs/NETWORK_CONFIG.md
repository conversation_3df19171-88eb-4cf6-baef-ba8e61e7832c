# Network Configuration for Cloud-Init

This document describes how to use the network configuration functionality in the cloud-init utility.

## Overview

The cloud-init utility now supports defining network configurations that will be included in the generated cloud-config YAML. This allows you to configure network interfaces, IP addresses, DNS settings, and more during VM initialization.

## Basic Usage

### DHCP Interface

To configure an interface to use DHCP:

```typescript
import { cloudInit, DhcpInterface, renderCloudInit } from "@suse-tmm/utils";

const config = cloudInit(
    DhcpInterface("enp1s0")
);

console.log(renderCloudInit(config));
```

### Static Interface

To configure an interface with a static IP:

```typescript
import { cloudInit, StaticInterface, renderCloudInit } from "@suse-tmm/utils";

const config = cloudInit(
    StaticInterface(
        "enp1s0",           // interface name
        "*************",    // IP address
        "*************",    // netmask
        "***********",      // gateway (optional)
        ["*******", "*******"], // DNS servers (optional)
        "00:11:22:33:44:55" // MAC address (optional)
    )
);
```

### Multiple Interfaces

You can configure multiple interfaces by chaining processor functions:

```typescript
const config = cloudInit(
    DhcpInterface("enp1s0"),
    StaticInterface("enp2s0", "**********", "*************", "********")
);
```

## Advanced Usage

### Pre-built Network Configuration

For more complex scenarios, you can create a complete network configuration and apply it:

```typescript
import { 
    cloudInit, 
    NetworkConfiguration, 
    createNetworkConfig, 
    createDhcpInterface, 
    createStaticInterface 
} from "@suse-tmm/utils";

const networkConfig = createNetworkConfig([
    createDhcpInterface("enp1s0", "00:11:22:33:44:55"),
    createStaticInterface("enp2s0", "*************", "*************")
]);

const config = cloudInit(
    NetworkConfiguration(networkConfig)
);
```

### Custom Network Configuration

You can also define a completely custom network configuration:

```typescript
const config = cloudInit(
    NetworkConfiguration({
        version: 1,
        config: [
            {
                type: "physical",
                name: "enp1s0",
                subnets: [{ type: "dhcp" }]
            },
            {
                type: "physical",
                name: "enp2s0",
                subnets: [{
                    type: "static",
                    address: "*************",
                    netmask: "*************",
                    gateway: "***********",
                    dns_nameservers: ["*******", "*******"]
                }]
            }
        ]
    })
);
```

## Generated Output

The network configuration will be included in the cloud-config YAML under the `network` section:

```yaml
#cloud-config
network:
  version: 1
  config:
    - type: physical
      name: enp1s0
      subnets:
        - type: dhcp
    - type: physical
      name: enp2s0
      subnets:
        - type: static
          address: *************
          netmask: *************
          gateway: ***********
          dns_nameservers:
            - *******
            - *******
```

## Supported Interface Types

- **physical**: Physical network interfaces
- **bond**: Bonded interfaces (future enhancement)
- **bridge**: Bridge interfaces (future enhancement)
- **vlan**: VLAN interfaces (future enhancement)

## Supported Subnet Types

- **dhcp**: Dynamic IP configuration via DHCP
- **static**: Static IP configuration
- **dhcp6**: IPv6 DHCP (future enhancement)
- **static6**: Static IPv6 configuration (future enhancement)

## API Reference

### Functions

- `DhcpInterface(name: string, macAddress?: string)`: Creates a DHCP interface processor
- `StaticInterface(name, address, netmask, gateway?, dnsServers?, macAddress?)`: Creates a static interface processor
- `NetworkConfiguration(networkConfig: NetworkConfig)`: Applies a complete network configuration
- `createDhcpInterface(name: string, macAddress?: string)`: Creates a DHCP interface object
- `createStaticInterface(...)`: Creates a static interface object
- `createNetworkConfig(interfaces: NetworkInterface[], version?)`: Creates a network configuration object

### Types

- `NetworkConfig`: Complete network configuration
- `NetworkInterface`: Individual network interface configuration
- `NetworkSubnet`: Subnet configuration for an interface

For more examples, see `examples/network-config-example.ts`.

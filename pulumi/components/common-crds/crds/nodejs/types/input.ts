// *** WARNING: this file was generated by crd2pulumi. ***
// *** Do not edit by hand unless you're certain you know what you are doing! ***

import * as pulumi from "@pulumi/pulumi";
import * as inputs from "./input";
import * as outputs from "./output";

export namespace cert_manager {
    export namespace v1 {
        /**
         * A Certificate resource should be created to ensure an up to date and signed
         * X.509 certificate is stored in the Kubernetes Secret resource named in `spec.secretName`.
         *
         * The stored certificate will be renewed before it expires (as configured by `spec.renewBefore`).
         */
        export interface Certificate {
            /**
             * APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
             */
            apiVersion?: pulumi.Input<"cert-manager.io/v1">;
            /**
             * Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
             */
            kind?: pulumi.Input<"Certificate">;
            /**
             * Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
             */
            metadata?: pulumi.Input<inputs.meta.v1.ObjectMeta>;
            spec?: pulumi.Input<inputs.cert_manager.v1.CertificateSpec>;
            status?: pulumi.Input<inputs.cert_manager.v1.CertificateStatus>;
        }

        /**
         * Specification of the desired state of the Certificate resource.
         * https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
         */
        export interface CertificateSpec {
            /**
             * Defines extra output formats of the private key and signed certificate chain
             * to be written to this Certificate's target Secret.
             *
             * This is a Beta Feature enabled by default. It can be disabled with the
             * `--feature-gates=AdditionalCertificateOutputFormats=false` option set on both
             * the controller and webhook components.
             */
            additionalOutputFormats?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.CertificateSpecAdditionalOutputFormats>[]>;
            /**
             * Requested common name X509 certificate subject attribute.
             * More info: https://datatracker.ietf.org/doc/html/rfc5280#section-*******
             * NOTE: TLS clients will ignore this value when any subject alternative name is
             * set (see https://tools.ietf.org/html/rfc6125#section-6.4.4).
             *
             * Should have a length of 64 characters or fewer to avoid generating invalid CSRs.
             * Cannot be set if the `literalSubject` field is set.
             */
            commonName?: pulumi.Input<string>;
            /**
             * Requested DNS subject alternative names.
             */
            dnsNames?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Requested 'duration' (i.e. lifetime) of the Certificate. Note that the
             * issuer may choose to ignore the requested duration, just like any other
             * requested attribute.
             *
             * If unset, this defaults to 90 days.
             * Minimum accepted duration is 1 hour.
             * Value must be in units accepted by Go time.ParseDuration https://golang.org/pkg/time/#ParseDuration.
             */
            duration?: pulumi.Input<string>;
            /**
             * Requested email subject alternative names.
             */
            emailAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Whether the KeyUsage and ExtKeyUsage extensions should be set in the encoded CSR.
             *
             * This option defaults to true, and should only be disabled if the target
             * issuer does not support CSRs with these X509 KeyUsage/ ExtKeyUsage extensions.
             */
            encodeUsagesInRequest?: pulumi.Input<boolean>;
            /**
             * Requested IP address subject alternative names.
             */
            ipAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Requested basic constraints isCA value.
             * The isCA value is used to set the `isCA` field on the created CertificateRequest
             * resources. Note that the issuer may choose to ignore the requested isCA value, just
             * like any other requested attribute.
             *
             * If true, this will automatically add the `cert sign` usage to the list
             * of requested `usages`.
             */
            isCA?: pulumi.Input<boolean>;
            issuerRef?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecIssuerRef>;
            keystores?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystores>;
            /**
             * Requested X.509 certificate subject, represented using the LDAP "String
             * Representation of a Distinguished Name" [1].
             * Important: the LDAP string format also specifies the order of the attributes
             * in the subject, this is important when issuing certs for LDAP authentication.
             * Example: `CN=foo,DC=corp,DC=example,DC=com`
             * More info [1]: https://datatracker.ietf.org/doc/html/rfc4514
             * More info: https://github.com/cert-manager/cert-manager/issues/3203
             * More info: https://github.com/cert-manager/cert-manager/issues/4424
             *
             * Cannot be set if the `subject` or `commonName` field is set.
             */
            literalSubject?: pulumi.Input<string>;
            nameConstraints?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecNameConstraints>;
            /**
             * `otherNames` is an escape hatch for SAN that allows any type. We currently restrict the support to string like otherNames, cf RFC 5280 p 37
             * Any UTF8 String valued otherName can be passed with by setting the keys oid: x.x.x.x and UTF8Value: somevalue for `otherName`.
             * Most commonly this would be UPN set with oid: 1.3.6.1.4.1.311.20.2.3
             * You should ensure that any OID passed is valid for the UTF8String type as we do not explicitly validate this.
             */
            otherNames?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.CertificateSpecOtherNames>[]>;
            privateKey?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecPrivateKey>;
            /**
             * How long before the currently issued certificate's expiry cert-manager should
             * renew the certificate. For example, if a certificate is valid for 60 minutes,
             * and `renewBefore=10m`, cert-manager will begin to attempt to renew the certificate
             * 50 minutes after it was issued (i.e. when there are 10 minutes remaining until
             * the certificate is no longer valid).
             *
             * NOTE: The actual lifetime of the issued certificate is used to determine the
             * renewal time. If an issuer returns a certificate with a different lifetime than
             * the one requested, cert-manager will use the lifetime of the issued certificate.
             *
             * If unset, this defaults to 1/3 of the issued certificate's lifetime.
             * Minimum accepted value is 5 minutes.
             * Value must be in units accepted by Go time.ParseDuration https://golang.org/pkg/time/#ParseDuration.
             * Cannot be set if the `renewBeforePercentage` field is set.
             */
            renewBefore?: pulumi.Input<string>;
            /**
             * `renewBeforePercentage` is like `renewBefore`, except it is a relative percentage
             * rather than an absolute duration. For example, if a certificate is valid for 60
             * minutes, and  `renewBeforePercentage=25`, cert-manager will begin to attempt to
             * renew the certificate 45 minutes after it was issued (i.e. when there are 15
             * minutes (25%) remaining until the certificate is no longer valid).
             *
             * NOTE: The actual lifetime of the issued certificate is used to determine the
             * renewal time. If an issuer returns a certificate with a different lifetime than
             * the one requested, cert-manager will use the lifetime of the issued certificate.
             *
             * Value must be an integer in the range (0,100). The minimum effective
             * `renewBefore` derived from the `renewBeforePercentage` and `duration` fields is 5
             * minutes.
             * Cannot be set if the `renewBefore` field is set.
             */
            renewBeforePercentage?: pulumi.Input<number>;
            /**
             * The maximum number of CertificateRequest revisions that are maintained in
             * the Certificate's history. Each revision represents a single `CertificateRequest`
             * created by this Certificate, either when it was created, renewed, or Spec
             * was changed. Revisions will be removed by oldest first if the number of
             * revisions exceeds this number.
             *
             * If set, revisionHistoryLimit must be a value of `1` or greater.
             * If unset (`nil`), revisions will not be garbage collected.
             * Default value is `nil`.
             */
            revisionHistoryLimit?: pulumi.Input<number>;
            /**
             * Name of the Secret resource that will be automatically created and
             * managed by this Certificate resource. It will be populated with a
             * private key and certificate, signed by the denoted issuer. The Secret
             * resource lives in the same namespace as the Certificate resource.
             */
            secretName?: pulumi.Input<string>;
            secretTemplate?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecSecretTemplate>;
            subject?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecSubject>;
            /**
             * Requested URI subject alternative names.
             */
            uris?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Requested key usages and extended key usages.
             * These usages are used to set the `usages` field on the created CertificateRequest
             * resources. If `encodeUsagesInRequest` is unset or set to `true`, the usages
             * will additionally be encoded in the `request` field which contains the CSR blob.
             *
             * If unset, defaults to `digital signature` and `key encipherment`.
             */
            usages?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * CertificateAdditionalOutputFormat defines an additional output format of a
         * Certificate resource. These contain supplementary data formats of the signed
         * certificate chain and paired private key.
         */
        export interface CertificateSpecAdditionalOutputFormats {
            /**
             * Type is the name of the format type that should be written to the
             * Certificate's target Secret.
             */
            type?: pulumi.Input<string>;
        }

        /**
         * CertificateAdditionalOutputFormat defines an additional output format of a
         * Certificate resource. These contain supplementary data formats of the signed
         * certificate chain and paired private key.
         */
        export interface CertificateSpecAdditionalOutputFormatsPatch {
            /**
             * Type is the name of the format type that should be written to the
             * Certificate's target Secret.
             */
            type?: pulumi.Input<string>;
        }

        /**
         * Reference to the issuer responsible for issuing the certificate.
         * If the issuer is namespace-scoped, it must be in the same namespace
         * as the Certificate. If the issuer is cluster-scoped, it can be used
         * from any namespace.
         *
         * The `name` field of the reference must always be specified.
         */
        export interface CertificateSpecIssuerRef {
            /**
             * Group of the resource being referred to.
             */
            group?: pulumi.Input<string>;
            /**
             * Kind of the resource being referred to.
             */
            kind?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to the issuer responsible for issuing the certificate.
         * If the issuer is namespace-scoped, it must be in the same namespace
         * as the Certificate. If the issuer is cluster-scoped, it can be used
         * from any namespace.
         *
         * The `name` field of the reference must always be specified.
         */
        export interface CertificateSpecIssuerRefPatch {
            /**
             * Group of the resource being referred to.
             */
            group?: pulumi.Input<string>;
            /**
             * Kind of the resource being referred to.
             */
            kind?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Additional keystore output formats to be stored in the Certificate's Secret.
         */
        export interface CertificateSpecKeystores {
            jks?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresJks>;
            pkcs12?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresPkcs12>;
        }

        /**
         * JKS configures options for storing a JKS keystore in the
         * `spec.secretName` Secret resource.
         */
        export interface CertificateSpecKeystoresJks {
            /**
             * Alias specifies the alias of the key in the keystore, required by the JKS format.
             * If not provided, the default alias `certificate` will be used.
             */
            alias?: pulumi.Input<string>;
            /**
             * Create enables JKS keystore creation for the Certificate.
             * If true, a file named `keystore.jks` will be created in the target
             * Secret resource, encrypted using the password stored in
             * `passwordSecretRef` or `password`.
             * The keystore file will be updated immediately.
             * If the issuer provided a CA certificate, a file named `truststore.jks`
             * will also be created in the target Secret resource, encrypted using the
             * password stored in `passwordSecretRef`
             * containing the issuing Certificate Authority
             */
            create?: pulumi.Input<boolean>;
            /**
             * Password provides a literal password used to encrypt the JKS keystore.
             * Mutually exclusive with passwordSecretRef.
             * One of password or passwordSecretRef must provide a password with a non-zero length.
             */
            password?: pulumi.Input<string>;
            passwordSecretRef?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresJksPasswordSecretRef>;
        }

        /**
         * PasswordSecretRef is a reference to a non-empty key in a Secret resource
         * containing the password used to encrypt the JKS keystore.
         * Mutually exclusive with password.
         * One of password or passwordSecretRef must provide a password with a non-zero length.
         */
        export interface CertificateSpecKeystoresJksPasswordSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * PasswordSecretRef is a reference to a non-empty key in a Secret resource
         * containing the password used to encrypt the JKS keystore.
         * Mutually exclusive with password.
         * One of password or passwordSecretRef must provide a password with a non-zero length.
         */
        export interface CertificateSpecKeystoresJksPasswordSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * JKS configures options for storing a JKS keystore in the
         * `spec.secretName` Secret resource.
         */
        export interface CertificateSpecKeystoresJksPatch {
            /**
             * Alias specifies the alias of the key in the keystore, required by the JKS format.
             * If not provided, the default alias `certificate` will be used.
             */
            alias?: pulumi.Input<string>;
            /**
             * Create enables JKS keystore creation for the Certificate.
             * If true, a file named `keystore.jks` will be created in the target
             * Secret resource, encrypted using the password stored in
             * `passwordSecretRef` or `password`.
             * The keystore file will be updated immediately.
             * If the issuer provided a CA certificate, a file named `truststore.jks`
             * will also be created in the target Secret resource, encrypted using the
             * password stored in `passwordSecretRef`
             * containing the issuing Certificate Authority
             */
            create?: pulumi.Input<boolean>;
            /**
             * Password provides a literal password used to encrypt the JKS keystore.
             * Mutually exclusive with passwordSecretRef.
             * One of password or passwordSecretRef must provide a password with a non-zero length.
             */
            password?: pulumi.Input<string>;
            passwordSecretRef?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresJksPasswordSecretRefPatch>;
        }

        /**
         * Additional keystore output formats to be stored in the Certificate's Secret.
         */
        export interface CertificateSpecKeystoresPatch {
            jks?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresJksPatch>;
            pkcs12?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresPkcs12Patch>;
        }

        /**
         * PKCS12 configures options for storing a PKCS12 keystore in the
         * `spec.secretName` Secret resource.
         */
        export interface CertificateSpecKeystoresPkcs12 {
            /**
             * Create enables PKCS12 keystore creation for the Certificate.
             * If true, a file named `keystore.p12` will be created in the target
             * Secret resource, encrypted using the password stored in
             * `passwordSecretRef` or in `password`.
             * The keystore file will be updated immediately.
             * If the issuer provided a CA certificate, a file named `truststore.p12` will
             * also be created in the target Secret resource, encrypted using the
             * password stored in `passwordSecretRef` containing the issuing Certificate
             * Authority
             */
            create?: pulumi.Input<boolean>;
            /**
             * Password provides a literal password used to encrypt the PKCS#12 keystore.
             * Mutually exclusive with passwordSecretRef.
             * One of password or passwordSecretRef must provide a password with a non-zero length.
             */
            password?: pulumi.Input<string>;
            passwordSecretRef?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresPkcs12PasswordSecretRef>;
            /**
             * Profile specifies the key and certificate encryption algorithms and the HMAC algorithm
             * used to create the PKCS12 keystore. Default value is `LegacyRC2` for backward compatibility.
             *
             * If provided, allowed values are:
             * `LegacyRC2`: Deprecated. Not supported by default in OpenSSL 3 or Java 20.
             * `LegacyDES`: Less secure algorithm. Use this option for maximal compatibility.
             * `Modern2023`: Secure algorithm. Use this option in case you have to always use secure algorithms
             * (eg. because of company policy). Please note that the security of the algorithm is not that important
             * in reality, because the unencrypted certificate and private key are also stored in the Secret.
             */
            profile?: pulumi.Input<string>;
        }

        /**
         * PasswordSecretRef is a reference to a non-empty key in a Secret resource
         * containing the password used to encrypt the PKCS#12 keystore.
         * Mutually exclusive with password.
         * One of password or passwordSecretRef must provide a password with a non-zero length.
         */
        export interface CertificateSpecKeystoresPkcs12PasswordSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * PasswordSecretRef is a reference to a non-empty key in a Secret resource
         * containing the password used to encrypt the PKCS#12 keystore.
         * Mutually exclusive with password.
         * One of password or passwordSecretRef must provide a password with a non-zero length.
         */
        export interface CertificateSpecKeystoresPkcs12PasswordSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * PKCS12 configures options for storing a PKCS12 keystore in the
         * `spec.secretName` Secret resource.
         */
        export interface CertificateSpecKeystoresPkcs12Patch {
            /**
             * Create enables PKCS12 keystore creation for the Certificate.
             * If true, a file named `keystore.p12` will be created in the target
             * Secret resource, encrypted using the password stored in
             * `passwordSecretRef` or in `password`.
             * The keystore file will be updated immediately.
             * If the issuer provided a CA certificate, a file named `truststore.p12` will
             * also be created in the target Secret resource, encrypted using the
             * password stored in `passwordSecretRef` containing the issuing Certificate
             * Authority
             */
            create?: pulumi.Input<boolean>;
            /**
             * Password provides a literal password used to encrypt the PKCS#12 keystore.
             * Mutually exclusive with passwordSecretRef.
             * One of password or passwordSecretRef must provide a password with a non-zero length.
             */
            password?: pulumi.Input<string>;
            passwordSecretRef?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresPkcs12PasswordSecretRefPatch>;
            /**
             * Profile specifies the key and certificate encryption algorithms and the HMAC algorithm
             * used to create the PKCS12 keystore. Default value is `LegacyRC2` for backward compatibility.
             *
             * If provided, allowed values are:
             * `LegacyRC2`: Deprecated. Not supported by default in OpenSSL 3 or Java 20.
             * `LegacyDES`: Less secure algorithm. Use this option for maximal compatibility.
             * `Modern2023`: Secure algorithm. Use this option in case you have to always use secure algorithms
             * (eg. because of company policy). Please note that the security of the algorithm is not that important
             * in reality, because the unencrypted certificate and private key are also stored in the Secret.
             */
            profile?: pulumi.Input<string>;
        }

        /**
         * x.509 certificate NameConstraint extension which MUST NOT be used in a non-CA certificate.
         * More Info: https://datatracker.ietf.org/doc/html/rfc5280#section-********
         *
         * This is an Alpha Feature and is only enabled with the
         * `--feature-gates=NameConstraints=true` option set on both
         * the controller and webhook components.
         */
        export interface CertificateSpecNameConstraints {
            /**
             * if true then the name constraints are marked critical.
             */
            critical?: pulumi.Input<boolean>;
            excluded?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecNameConstraintsExcluded>;
            permitted?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecNameConstraintsPermitted>;
        }

        /**
         * Excluded contains the constraints which must be disallowed. Any name matching a
         * restriction in the excluded field is invalid regardless
         * of information appearing in the permitted
         */
        export interface CertificateSpecNameConstraintsExcluded {
            /**
             * DNSDomains is a list of DNS domains that are permitted or excluded.
             */
            dnsDomains?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * EmailAddresses is a list of Email Addresses that are permitted or excluded.
             */
            emailAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * IPRanges is a list of IP Ranges that are permitted or excluded.
             * This should be a valid CIDR notation.
             */
            ipRanges?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * URIDomains is a list of URI domains that are permitted or excluded.
             */
            uriDomains?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * Excluded contains the constraints which must be disallowed. Any name matching a
         * restriction in the excluded field is invalid regardless
         * of information appearing in the permitted
         */
        export interface CertificateSpecNameConstraintsExcludedPatch {
            /**
             * DNSDomains is a list of DNS domains that are permitted or excluded.
             */
            dnsDomains?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * EmailAddresses is a list of Email Addresses that are permitted or excluded.
             */
            emailAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * IPRanges is a list of IP Ranges that are permitted or excluded.
             * This should be a valid CIDR notation.
             */
            ipRanges?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * URIDomains is a list of URI domains that are permitted or excluded.
             */
            uriDomains?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * x.509 certificate NameConstraint extension which MUST NOT be used in a non-CA certificate.
         * More Info: https://datatracker.ietf.org/doc/html/rfc5280#section-********
         *
         * This is an Alpha Feature and is only enabled with the
         * `--feature-gates=NameConstraints=true` option set on both
         * the controller and webhook components.
         */
        export interface CertificateSpecNameConstraintsPatch {
            /**
             * if true then the name constraints are marked critical.
             */
            critical?: pulumi.Input<boolean>;
            excluded?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecNameConstraintsExcludedPatch>;
            permitted?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecNameConstraintsPermittedPatch>;
        }

        /**
         * Permitted contains the constraints in which the names must be located.
         */
        export interface CertificateSpecNameConstraintsPermitted {
            /**
             * DNSDomains is a list of DNS domains that are permitted or excluded.
             */
            dnsDomains?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * EmailAddresses is a list of Email Addresses that are permitted or excluded.
             */
            emailAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * IPRanges is a list of IP Ranges that are permitted or excluded.
             * This should be a valid CIDR notation.
             */
            ipRanges?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * URIDomains is a list of URI domains that are permitted or excluded.
             */
            uriDomains?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * Permitted contains the constraints in which the names must be located.
         */
        export interface CertificateSpecNameConstraintsPermittedPatch {
            /**
             * DNSDomains is a list of DNS domains that are permitted or excluded.
             */
            dnsDomains?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * EmailAddresses is a list of Email Addresses that are permitted or excluded.
             */
            emailAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * IPRanges is a list of IP Ranges that are permitted or excluded.
             * This should be a valid CIDR notation.
             */
            ipRanges?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * URIDomains is a list of URI domains that are permitted or excluded.
             */
            uriDomains?: pulumi.Input<pulumi.Input<string>[]>;
        }

        export interface CertificateSpecOtherNames {
            /**
             * OID is the object identifier for the otherName SAN.
             * The object identifier must be expressed as a dotted string, for
             * example, "1.2.840.113556.1.4.221".
             */
            oid?: pulumi.Input<string>;
            /**
             * utf8Value is the string value of the otherName SAN.
             * The utf8Value accepts any valid UTF8 string to set as value for the otherName SAN.
             */
            utf8Value?: pulumi.Input<string>;
        }

        export interface CertificateSpecOtherNamesPatch {
            /**
             * OID is the object identifier for the otherName SAN.
             * The object identifier must be expressed as a dotted string, for
             * example, "1.2.840.113556.1.4.221".
             */
            oid?: pulumi.Input<string>;
            /**
             * utf8Value is the string value of the otherName SAN.
             * The utf8Value accepts any valid UTF8 string to set as value for the otherName SAN.
             */
            utf8Value?: pulumi.Input<string>;
        }

        /**
         * Specification of the desired state of the Certificate resource.
         * https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
         */
        export interface CertificateSpecPatch {
            /**
             * Defines extra output formats of the private key and signed certificate chain
             * to be written to this Certificate's target Secret.
             *
             * This is a Beta Feature enabled by default. It can be disabled with the
             * `--feature-gates=AdditionalCertificateOutputFormats=false` option set on both
             * the controller and webhook components.
             */
            additionalOutputFormats?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.CertificateSpecAdditionalOutputFormatsPatch>[]>;
            /**
             * Requested common name X509 certificate subject attribute.
             * More info: https://datatracker.ietf.org/doc/html/rfc5280#section-*******
             * NOTE: TLS clients will ignore this value when any subject alternative name is
             * set (see https://tools.ietf.org/html/rfc6125#section-6.4.4).
             *
             * Should have a length of 64 characters or fewer to avoid generating invalid CSRs.
             * Cannot be set if the `literalSubject` field is set.
             */
            commonName?: pulumi.Input<string>;
            /**
             * Requested DNS subject alternative names.
             */
            dnsNames?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Requested 'duration' (i.e. lifetime) of the Certificate. Note that the
             * issuer may choose to ignore the requested duration, just like any other
             * requested attribute.
             *
             * If unset, this defaults to 90 days.
             * Minimum accepted duration is 1 hour.
             * Value must be in units accepted by Go time.ParseDuration https://golang.org/pkg/time/#ParseDuration.
             */
            duration?: pulumi.Input<string>;
            /**
             * Requested email subject alternative names.
             */
            emailAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Whether the KeyUsage and ExtKeyUsage extensions should be set in the encoded CSR.
             *
             * This option defaults to true, and should only be disabled if the target
             * issuer does not support CSRs with these X509 KeyUsage/ ExtKeyUsage extensions.
             */
            encodeUsagesInRequest?: pulumi.Input<boolean>;
            /**
             * Requested IP address subject alternative names.
             */
            ipAddresses?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Requested basic constraints isCA value.
             * The isCA value is used to set the `isCA` field on the created CertificateRequest
             * resources. Note that the issuer may choose to ignore the requested isCA value, just
             * like any other requested attribute.
             *
             * If true, this will automatically add the `cert sign` usage to the list
             * of requested `usages`.
             */
            isCA?: pulumi.Input<boolean>;
            issuerRef?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecIssuerRefPatch>;
            keystores?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecKeystoresPatch>;
            /**
             * Requested X.509 certificate subject, represented using the LDAP "String
             * Representation of a Distinguished Name" [1].
             * Important: the LDAP string format also specifies the order of the attributes
             * in the subject, this is important when issuing certs for LDAP authentication.
             * Example: `CN=foo,DC=corp,DC=example,DC=com`
             * More info [1]: https://datatracker.ietf.org/doc/html/rfc4514
             * More info: https://github.com/cert-manager/cert-manager/issues/3203
             * More info: https://github.com/cert-manager/cert-manager/issues/4424
             *
             * Cannot be set if the `subject` or `commonName` field is set.
             */
            literalSubject?: pulumi.Input<string>;
            nameConstraints?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecNameConstraintsPatch>;
            /**
             * `otherNames` is an escape hatch for SAN that allows any type. We currently restrict the support to string like otherNames, cf RFC 5280 p 37
             * Any UTF8 String valued otherName can be passed with by setting the keys oid: x.x.x.x and UTF8Value: somevalue for `otherName`.
             * Most commonly this would be UPN set with oid: 1.3.6.1.4.1.311.20.2.3
             * You should ensure that any OID passed is valid for the UTF8String type as we do not explicitly validate this.
             */
            otherNames?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.CertificateSpecOtherNamesPatch>[]>;
            privateKey?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecPrivateKeyPatch>;
            /**
             * How long before the currently issued certificate's expiry cert-manager should
             * renew the certificate. For example, if a certificate is valid for 60 minutes,
             * and `renewBefore=10m`, cert-manager will begin to attempt to renew the certificate
             * 50 minutes after it was issued (i.e. when there are 10 minutes remaining until
             * the certificate is no longer valid).
             *
             * NOTE: The actual lifetime of the issued certificate is used to determine the
             * renewal time. If an issuer returns a certificate with a different lifetime than
             * the one requested, cert-manager will use the lifetime of the issued certificate.
             *
             * If unset, this defaults to 1/3 of the issued certificate's lifetime.
             * Minimum accepted value is 5 minutes.
             * Value must be in units accepted by Go time.ParseDuration https://golang.org/pkg/time/#ParseDuration.
             * Cannot be set if the `renewBeforePercentage` field is set.
             */
            renewBefore?: pulumi.Input<string>;
            /**
             * `renewBeforePercentage` is like `renewBefore`, except it is a relative percentage
             * rather than an absolute duration. For example, if a certificate is valid for 60
             * minutes, and  `renewBeforePercentage=25`, cert-manager will begin to attempt to
             * renew the certificate 45 minutes after it was issued (i.e. when there are 15
             * minutes (25%) remaining until the certificate is no longer valid).
             *
             * NOTE: The actual lifetime of the issued certificate is used to determine the
             * renewal time. If an issuer returns a certificate with a different lifetime than
             * the one requested, cert-manager will use the lifetime of the issued certificate.
             *
             * Value must be an integer in the range (0,100). The minimum effective
             * `renewBefore` derived from the `renewBeforePercentage` and `duration` fields is 5
             * minutes.
             * Cannot be set if the `renewBefore` field is set.
             */
            renewBeforePercentage?: pulumi.Input<number>;
            /**
             * The maximum number of CertificateRequest revisions that are maintained in
             * the Certificate's history. Each revision represents a single `CertificateRequest`
             * created by this Certificate, either when it was created, renewed, or Spec
             * was changed. Revisions will be removed by oldest first if the number of
             * revisions exceeds this number.
             *
             * If set, revisionHistoryLimit must be a value of `1` or greater.
             * If unset (`nil`), revisions will not be garbage collected.
             * Default value is `nil`.
             */
            revisionHistoryLimit?: pulumi.Input<number>;
            /**
             * Name of the Secret resource that will be automatically created and
             * managed by this Certificate resource. It will be populated with a
             * private key and certificate, signed by the denoted issuer. The Secret
             * resource lives in the same namespace as the Certificate resource.
             */
            secretName?: pulumi.Input<string>;
            secretTemplate?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecSecretTemplatePatch>;
            subject?: pulumi.Input<inputs.cert_manager.v1.CertificateSpecSubjectPatch>;
            /**
             * Requested URI subject alternative names.
             */
            uris?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Requested key usages and extended key usages.
             * These usages are used to set the `usages` field on the created CertificateRequest
             * resources. If `encodeUsagesInRequest` is unset or set to `true`, the usages
             * will additionally be encoded in the `request` field which contains the CSR blob.
             *
             * If unset, defaults to `digital signature` and `key encipherment`.
             */
            usages?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * Private key options. These include the key algorithm and size, the used
         * encoding and the rotation policy.
         */
        export interface CertificateSpecPrivateKey {
            /**
             * Algorithm is the private key algorithm of the corresponding private key
             * for this certificate.
             *
             * If provided, allowed values are either `RSA`, `ECDSA` or `Ed25519`.
             * If `algorithm` is specified and `size` is not provided,
             * key size of 2048 will be used for `RSA` key algorithm and
             * key size of 256 will be used for `ECDSA` key algorithm.
             * key size is ignored when using the `Ed25519` key algorithm.
             */
            algorithm?: pulumi.Input<string>;
            /**
             * The private key cryptography standards (PKCS) encoding for this
             * certificate's private key to be encoded in.
             *
             * If provided, allowed values are `PKCS1` and `PKCS8` standing for PKCS#1
             * and PKCS#8, respectively.
             * Defaults to `PKCS1` if not specified.
             */
            encoding?: pulumi.Input<string>;
            /**
             * RotationPolicy controls how private keys should be regenerated when a
             * re-issuance is being processed.
             *
             * If set to `Never`, a private key will only be generated if one does not
             * already exist in the target `spec.secretName`. If one does exist but it
             * does not have the correct algorithm or size, a warning will be raised
             * to await user intervention.
             * If set to `Always`, a private key matching the specified requirements
             * will be generated whenever a re-issuance occurs.
             * Default is `Never` for backward compatibility.
             */
            rotationPolicy?: pulumi.Input<string>;
            /**
             * Size is the key bit size of the corresponding private key for this certificate.
             *
             * If `algorithm` is set to `RSA`, valid values are `2048`, `4096` or `8192`,
             * and will default to `2048` if not specified.
             * If `algorithm` is set to `ECDSA`, valid values are `256`, `384` or `521`,
             * and will default to `256` if not specified.
             * If `algorithm` is set to `Ed25519`, Size is ignored.
             * No other values are allowed.
             */
            size?: pulumi.Input<number>;
        }

        /**
         * Private key options. These include the key algorithm and size, the used
         * encoding and the rotation policy.
         */
        export interface CertificateSpecPrivateKeyPatch {
            /**
             * Algorithm is the private key algorithm of the corresponding private key
             * for this certificate.
             *
             * If provided, allowed values are either `RSA`, `ECDSA` or `Ed25519`.
             * If `algorithm` is specified and `size` is not provided,
             * key size of 2048 will be used for `RSA` key algorithm and
             * key size of 256 will be used for `ECDSA` key algorithm.
             * key size is ignored when using the `Ed25519` key algorithm.
             */
            algorithm?: pulumi.Input<string>;
            /**
             * The private key cryptography standards (PKCS) encoding for this
             * certificate's private key to be encoded in.
             *
             * If provided, allowed values are `PKCS1` and `PKCS8` standing for PKCS#1
             * and PKCS#8, respectively.
             * Defaults to `PKCS1` if not specified.
             */
            encoding?: pulumi.Input<string>;
            /**
             * RotationPolicy controls how private keys should be regenerated when a
             * re-issuance is being processed.
             *
             * If set to `Never`, a private key will only be generated if one does not
             * already exist in the target `spec.secretName`. If one does exist but it
             * does not have the correct algorithm or size, a warning will be raised
             * to await user intervention.
             * If set to `Always`, a private key matching the specified requirements
             * will be generated whenever a re-issuance occurs.
             * Default is `Never` for backward compatibility.
             */
            rotationPolicy?: pulumi.Input<string>;
            /**
             * Size is the key bit size of the corresponding private key for this certificate.
             *
             * If `algorithm` is set to `RSA`, valid values are `2048`, `4096` or `8192`,
             * and will default to `2048` if not specified.
             * If `algorithm` is set to `ECDSA`, valid values are `256`, `384` or `521`,
             * and will default to `256` if not specified.
             * If `algorithm` is set to `Ed25519`, Size is ignored.
             * No other values are allowed.
             */
            size?: pulumi.Input<number>;
        }

        /**
         * Defines annotations and labels to be copied to the Certificate's Secret.
         * Labels and annotations on the Secret will be changed as they appear on the
         * SecretTemplate when added or removed. SecretTemplate annotations are added
         * in conjunction with, and cannot overwrite, the base set of annotations
         * cert-manager sets on the Certificate's Secret.
         */
        export interface CertificateSpecSecretTemplate {
            /**
             * Annotations is a key value map to be copied to the target Kubernetes Secret.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels is a key value map to be copied to the target Kubernetes Secret.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Defines annotations and labels to be copied to the Certificate's Secret.
         * Labels and annotations on the Secret will be changed as they appear on the
         * SecretTemplate when added or removed. SecretTemplate annotations are added
         * in conjunction with, and cannot overwrite, the base set of annotations
         * cert-manager sets on the Certificate's Secret.
         */
        export interface CertificateSpecSecretTemplatePatch {
            /**
             * Annotations is a key value map to be copied to the target Kubernetes Secret.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels is a key value map to be copied to the target Kubernetes Secret.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Requested set of X509 certificate subject attributes.
         * More info: https://datatracker.ietf.org/doc/html/rfc5280#section-*******
         *
         * The common name attribute is specified separately in the `commonName` field.
         * Cannot be set if the `literalSubject` field is set.
         */
        export interface CertificateSpecSubject {
            /**
             * Countries to be used on the Certificate.
             */
            countries?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Cities to be used on the Certificate.
             */
            localities?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Organizational Units to be used on the Certificate.
             */
            organizationalUnits?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Organizations to be used on the Certificate.
             */
            organizations?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Postal codes to be used on the Certificate.
             */
            postalCodes?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * State/Provinces to be used on the Certificate.
             */
            provinces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Serial number to be used on the Certificate.
             */
            serialNumber?: pulumi.Input<string>;
            /**
             * Street addresses to be used on the Certificate.
             */
            streetAddresses?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * Requested set of X509 certificate subject attributes.
         * More info: https://datatracker.ietf.org/doc/html/rfc5280#section-*******
         *
         * The common name attribute is specified separately in the `commonName` field.
         * Cannot be set if the `literalSubject` field is set.
         */
        export interface CertificateSpecSubjectPatch {
            /**
             * Countries to be used on the Certificate.
             */
            countries?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Cities to be used on the Certificate.
             */
            localities?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Organizational Units to be used on the Certificate.
             */
            organizationalUnits?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Organizations to be used on the Certificate.
             */
            organizations?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Postal codes to be used on the Certificate.
             */
            postalCodes?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * State/Provinces to be used on the Certificate.
             */
            provinces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Serial number to be used on the Certificate.
             */
            serialNumber?: pulumi.Input<string>;
            /**
             * Street addresses to be used on the Certificate.
             */
            streetAddresses?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * Status of the Certificate.
         * This is set and managed automatically.
         * Read-only.
         * More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status
         */
        export interface CertificateStatus {
            /**
             * List of status conditions to indicate the status of certificates.
             * Known condition types are `Ready` and `Issuing`.
             */
            conditions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.CertificateStatusConditions>[]>;
            /**
             * The number of continuous failed issuance attempts up till now. This
             * field gets removed (if set) on a successful issuance and gets set to
             * 1 if unset and an issuance has failed. If an issuance has failed, the
             * delay till the next issuance will be calculated using formula
             * time.Hour * 2 ^ (failedIssuanceAttempts - 1).
             */
            failedIssuanceAttempts?: pulumi.Input<number>;
            /**
             * LastFailureTime is set only if the latest issuance for this
             * Certificate failed and contains the time of the failure. If an
             * issuance has failed, the delay till the next issuance will be
             * calculated using formula time.Hour * 2 ^ (failedIssuanceAttempts -
             * 1). If the latest issuance has succeeded this field will be unset.
             */
            lastFailureTime?: pulumi.Input<string>;
            /**
             * The name of the Secret resource containing the private key to be used
             * for the next certificate iteration.
             * The keymanager controller will automatically set this field if the
             * `Issuing` condition is set to `True`.
             * It will automatically unset this field when the Issuing condition is
             * not set or False.
             */
            nextPrivateKeySecretName?: pulumi.Input<string>;
            /**
             * The expiration time of the certificate stored in the secret named
             * by this resource in `spec.secretName`.
             */
            notAfter?: pulumi.Input<string>;
            /**
             * The time after which the certificate stored in the secret named
             * by this resource in `spec.secretName` is valid.
             */
            notBefore?: pulumi.Input<string>;
            /**
             * RenewalTime is the time at which the certificate will be next
             * renewed.
             * If not set, no upcoming renewal is scheduled.
             */
            renewalTime?: pulumi.Input<string>;
            /**
             * The current 'revision' of the certificate as issued.
             *
             * When a CertificateRequest resource is created, it will have the
             * `cert-manager.io/certificate-revision` set to one greater than the
             * current value of this field.
             *
             * Upon issuance, this field will be set to the value of the annotation
             * on the CertificateRequest resource used to issue the certificate.
             *
             * Persisting the value on the CertificateRequest resource allows the
             * certificates controller to know whether a request is part of an old
             * issuance or if it is part of the ongoing revision's issuance by
             * checking if the revision value in the annotation is greater than this
             * field.
             */
            revision?: pulumi.Input<number>;
        }

        /**
         * CertificateCondition contains condition information for a Certificate.
         */
        export interface CertificateStatusConditions {
            /**
             * LastTransitionTime is the timestamp corresponding to the last status
             * change of this condition.
             */
            lastTransitionTime?: pulumi.Input<string>;
            /**
             * Message is a human readable description of the details of the last
             * transition, complementing reason.
             */
            message?: pulumi.Input<string>;
            /**
             * If set, this represents the .metadata.generation that the condition was
             * set based upon.
             * For instance, if .metadata.generation is currently 12, but the
             * .status.condition[x].observedGeneration is 9, the condition is out of date
             * with respect to the current state of the Certificate.
             */
            observedGeneration?: pulumi.Input<number>;
            /**
             * Reason is a brief machine readable explanation for the condition's last
             * transition.
             */
            reason?: pulumi.Input<string>;
            /**
             * Status of the condition, one of (`True`, `False`, `Unknown`).
             */
            status?: pulumi.Input<string>;
            /**
             * Type of the condition, known values are (`Ready`, `Issuing`).
             */
            type?: pulumi.Input<string>;
        }

        /**
         * A ClusterIssuer represents a certificate issuing authority which can be
         * referenced as part of `issuerRef` fields.
         * It is similar to an Issuer, however it is cluster-scoped and therefore can
         * be referenced by resources that exist in *any* namespace, not just the same
         * namespace as the referent.
         */
        export interface ClusterIssuer {
            /**
             * APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
             */
            apiVersion?: pulumi.Input<"cert-manager.io/v1">;
            /**
             * Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
             */
            kind?: pulumi.Input<"ClusterIssuer">;
            /**
             * Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
             */
            metadata?: pulumi.Input<inputs.meta.v1.ObjectMeta>;
            spec?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpec>;
            status?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerStatus>;
        }

        /**
         * Desired state of the ClusterIssuer resource.
         */
        export interface ClusterIssuerSpec {
            acme?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcme>;
            ca?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecCa>;
            selfSigned?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecSelfSigned>;
            vault?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVault>;
            venafi?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafi>;
        }

        /**
         * ACME configures this issuer to communicate with a RFC8555 (ACME) server
         * to obtain signed x509 certificates.
         */
        export interface ClusterIssuerSpecAcme {
            /**
             * Base64-encoded bundle of PEM CAs which can be used to validate the certificate
             * chain presented by the ACME server.
             * Mutually exclusive with SkipTLSVerify; prefer using CABundle to prevent various
             * kinds of security vulnerabilities.
             * If CABundle and SkipTLSVerify are unset, the system certificate bundle inside
             * the container is used to validate the TLS connection.
             */
            caBundle?: pulumi.Input<string>;
            /**
             * Enables or disables generating a new ACME account key.
             * If true, the Issuer resource will *not* request a new account but will expect
             * the account key to be supplied via an existing secret.
             * If false, the cert-manager system will generate a new ACME account key
             * for the Issuer.
             * Defaults to false.
             */
            disableAccountKeyGeneration?: pulumi.Input<boolean>;
            /**
             * Email is the email address to be associated with the ACME account.
             * This field is optional, but it is strongly recommended to be set.
             * It will be used to contact you in case of issues with your account or
             * certificates, including expiry notification emails.
             * This field may be updated after the account is initially registered.
             */
            email?: pulumi.Input<string>;
            /**
             * Enables requesting a Not After date on certificates that matches the
             * duration of the certificate. This is not supported by all ACME servers
             * like Let's Encrypt. If set to true when the ACME server does not support
             * it, it will create an error on the Order.
             * Defaults to false.
             */
            enableDurationFeature?: pulumi.Input<boolean>;
            externalAccountBinding?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeExternalAccountBinding>;
            /**
             * PreferredChain is the chain to use if the ACME server outputs multiple.
             * PreferredChain is no guarantee that this one gets delivered by the ACME
             * endpoint.
             * For example, for Let's Encrypt's DST crosssign you would use:
             * "DST Root CA X3" or "ISRG Root X1" for the newer Let's Encrypt root CA.
             * This value picks the first certificate bundle in the combined set of
             * ACME default and alternative chains that has a root-most certificate with
             * this value as its issuer's commonname.
             */
            preferredChain?: pulumi.Input<string>;
            privateKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmePrivateKeySecretRef>;
            /**
             * Server is the URL used to access the ACME server's 'directory' endpoint.
             * For example, for Let's Encrypt's staging endpoint, you would use:
             * "https://acme-staging-v02.api.letsencrypt.org/directory".
             * Only ACME v2 endpoints (i.e. RFC 8555) are supported.
             */
            server?: pulumi.Input<string>;
            /**
             * INSECURE: Enables or disables validation of the ACME server TLS certificate.
             * If true, requests to the ACME server will not have the TLS certificate chain
             * validated.
             * Mutually exclusive with CABundle; prefer using CABundle to prevent various
             * kinds of security vulnerabilities.
             * Only enable this option in development environments.
             * If CABundle and SkipTLSVerify are unset, the system certificate bundle inside
             * the container is used to validate the TLS connection.
             * Defaults to false.
             */
            skipTLSVerify?: pulumi.Input<boolean>;
            /**
             * Solvers is a list of challenge solvers that will be used to solve
             * ACME challenges for the matching domains.
             * Solver configurations must be provided in order to obtain certificates
             * from an ACME server.
             * For more information, see: https://cert-manager.io/docs/configuration/acme/
             */
            solvers?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolvers>[]>;
        }

        /**
         * ExternalAccountBinding is a reference to a CA external account of the ACME
         * server.
         * If set, upon registration cert-manager will attempt to associate the given
         * external account credentials with the registered ACME account.
         */
        export interface ClusterIssuerSpecAcmeExternalAccountBinding {
            /**
             * Deprecated: keyAlgorithm field exists for historical compatibility
             * reasons and should not be used. The algorithm is now hardcoded to HS256
             * in golang/x/crypto/acme.
             */
            keyAlgorithm?: pulumi.Input<string>;
            /**
             * keyID is the ID of the CA key that the External Account is bound to.
             */
            keyID?: pulumi.Input<string>;
            keySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeExternalAccountBindingKeySecretRef>;
        }

        /**
         * keySecretRef is a Secret Key Selector referencing a data item in a Kubernetes
         * Secret which holds the symmetric MAC key of the External Account Binding.
         * The `key` is the index string that is paired with the key data in the
         * Secret and should not be confused with the key data itself, or indeed with
         * the External Account Binding keyID above.
         * The secret key stored in the Secret **must** be un-padded, base64 URL
         * encoded data.
         */
        export interface ClusterIssuerSpecAcmeExternalAccountBindingKeySecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * keySecretRef is a Secret Key Selector referencing a data item in a Kubernetes
         * Secret which holds the symmetric MAC key of the External Account Binding.
         * The `key` is the index string that is paired with the key data in the
         * Secret and should not be confused with the key data itself, or indeed with
         * the External Account Binding keyID above.
         * The secret key stored in the Secret **must** be un-padded, base64 URL
         * encoded data.
         */
        export interface ClusterIssuerSpecAcmeExternalAccountBindingKeySecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * ExternalAccountBinding is a reference to a CA external account of the ACME
         * server.
         * If set, upon registration cert-manager will attempt to associate the given
         * external account credentials with the registered ACME account.
         */
        export interface ClusterIssuerSpecAcmeExternalAccountBindingPatch {
            /**
             * Deprecated: keyAlgorithm field exists for historical compatibility
             * reasons and should not be used. The algorithm is now hardcoded to HS256
             * in golang/x/crypto/acme.
             */
            keyAlgorithm?: pulumi.Input<string>;
            /**
             * keyID is the ID of the CA key that the External Account is bound to.
             */
            keyID?: pulumi.Input<string>;
            keySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeExternalAccountBindingKeySecretRefPatch>;
        }

        /**
         * ACME configures this issuer to communicate with a RFC8555 (ACME) server
         * to obtain signed x509 certificates.
         */
        export interface ClusterIssuerSpecAcmePatch {
            /**
             * Base64-encoded bundle of PEM CAs which can be used to validate the certificate
             * chain presented by the ACME server.
             * Mutually exclusive with SkipTLSVerify; prefer using CABundle to prevent various
             * kinds of security vulnerabilities.
             * If CABundle and SkipTLSVerify are unset, the system certificate bundle inside
             * the container is used to validate the TLS connection.
             */
            caBundle?: pulumi.Input<string>;
            /**
             * Enables or disables generating a new ACME account key.
             * If true, the Issuer resource will *not* request a new account but will expect
             * the account key to be supplied via an existing secret.
             * If false, the cert-manager system will generate a new ACME account key
             * for the Issuer.
             * Defaults to false.
             */
            disableAccountKeyGeneration?: pulumi.Input<boolean>;
            /**
             * Email is the email address to be associated with the ACME account.
             * This field is optional, but it is strongly recommended to be set.
             * It will be used to contact you in case of issues with your account or
             * certificates, including expiry notification emails.
             * This field may be updated after the account is initially registered.
             */
            email?: pulumi.Input<string>;
            /**
             * Enables requesting a Not After date on certificates that matches the
             * duration of the certificate. This is not supported by all ACME servers
             * like Let's Encrypt. If set to true when the ACME server does not support
             * it, it will create an error on the Order.
             * Defaults to false.
             */
            enableDurationFeature?: pulumi.Input<boolean>;
            externalAccountBinding?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeExternalAccountBindingPatch>;
            /**
             * PreferredChain is the chain to use if the ACME server outputs multiple.
             * PreferredChain is no guarantee that this one gets delivered by the ACME
             * endpoint.
             * For example, for Let's Encrypt's DST crosssign you would use:
             * "DST Root CA X3" or "ISRG Root X1" for the newer Let's Encrypt root CA.
             * This value picks the first certificate bundle in the combined set of
             * ACME default and alternative chains that has a root-most certificate with
             * this value as its issuer's commonname.
             */
            preferredChain?: pulumi.Input<string>;
            privateKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmePrivateKeySecretRefPatch>;
            /**
             * Server is the URL used to access the ACME server's 'directory' endpoint.
             * For example, for Let's Encrypt's staging endpoint, you would use:
             * "https://acme-staging-v02.api.letsencrypt.org/directory".
             * Only ACME v2 endpoints (i.e. RFC 8555) are supported.
             */
            server?: pulumi.Input<string>;
            /**
             * INSECURE: Enables or disables validation of the ACME server TLS certificate.
             * If true, requests to the ACME server will not have the TLS certificate chain
             * validated.
             * Mutually exclusive with CABundle; prefer using CABundle to prevent various
             * kinds of security vulnerabilities.
             * Only enable this option in development environments.
             * If CABundle and SkipTLSVerify are unset, the system certificate bundle inside
             * the container is used to validate the TLS connection.
             * Defaults to false.
             */
            skipTLSVerify?: pulumi.Input<boolean>;
            /**
             * Solvers is a list of challenge solvers that will be used to solve
             * ACME challenges for the matching domains.
             * Solver configurations must be provided in order to obtain certificates
             * from an ACME server.
             * For more information, see: https://cert-manager.io/docs/configuration/acme/
             */
            solvers?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversPatch>[]>;
        }

        /**
         * PrivateKey is the name of a Kubernetes Secret resource that will be used to
         * store the automatically generated ACME account private key.
         * Optionally, a `key` may be specified to select a specific entry within
         * the named Secret resource.
         * If `key` is not specified, a default of `tls.key` will be used.
         */
        export interface ClusterIssuerSpecAcmePrivateKeySecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * PrivateKey is the name of a Kubernetes Secret resource that will be used to
         * store the automatically generated ACME account private key.
         * Optionally, a `key` may be specified to select a specific entry within
         * the named Secret resource.
         * If `key` is not specified, a default of `tls.key` will be used.
         */
        export interface ClusterIssuerSpecAcmePrivateKeySecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * An ACMEChallengeSolver describes how to solve ACME challenges for the issuer it is part of.
         * A selector may be provided to use different solving strategies for different DNS names.
         * Only one of HTTP01 or DNS01 must be provided.
         */
        export interface ClusterIssuerSpecAcmeSolvers {
            dns01?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01>;
            http01?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01>;
            selector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversSelector>;
        }

        /**
         * Configures cert-manager to attempt to complete authorizations by
         * performing the DNS01 challenge flow.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01 {
            acmeDNS?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AcmeDNS>;
            akamai?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Akamai>;
            azureDNS?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AzureDNS>;
            cloudDNS?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudDNS>;
            cloudflare?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Cloudflare>;
            /**
             * CNAMEStrategy configures how the DNS01 provider should handle CNAME
             * records when found in DNS zones.
             */
            cnameStrategy?: pulumi.Input<string>;
            digitalocean?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Digitalocean>;
            rfc2136?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Rfc2136>;
            route53?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53>;
            webhook?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Webhook>;
        }

        /**
         * Use the 'ACME DNS' (https://github.com/joohoi/acme-dns) API to manage
         * DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AcmeDNS {
            accountSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AcmeDNSAccountSecretRef>;
            host?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AcmeDNSAccountSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AcmeDNSAccountSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Use the 'ACME DNS' (https://github.com/joohoi/acme-dns) API to manage
         * DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AcmeDNSPatch {
            accountSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AcmeDNSAccountSecretRefPatch>;
            host?: pulumi.Input<string>;
        }

        /**
         * Use the Akamai DNS zone management API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Akamai {
            accessTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AkamaiAccessTokenSecretRef>;
            clientSecretSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AkamaiClientSecretSecretRef>;
            clientTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AkamaiClientTokenSecretRef>;
            serviceConsumerDomain?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AkamaiAccessTokenSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AkamaiAccessTokenSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AkamaiClientSecretSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AkamaiClientSecretSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AkamaiClientTokenSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AkamaiClientTokenSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Use the Akamai DNS zone management API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AkamaiPatch {
            accessTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AkamaiAccessTokenSecretRefPatch>;
            clientSecretSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AkamaiClientSecretSecretRefPatch>;
            clientTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AkamaiClientTokenSecretRefPatch>;
            serviceConsumerDomain?: pulumi.Input<string>;
        }

        /**
         * Use the Microsoft Azure DNS API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AzureDNS {
            /**
             * Auth: Azure Service Principal:
             * The ClientID of the Azure Service Principal used to authenticate with Azure DNS.
             * If set, ClientSecret and TenantID must also be set.
             */
            clientID?: pulumi.Input<string>;
            clientSecretSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AzureDNSClientSecretSecretRef>;
            /**
             * name of the Azure environment (default AzurePublicCloud)
             */
            environment?: pulumi.Input<string>;
            /**
             * name of the DNS zone that should be used
             */
            hostedZoneName?: pulumi.Input<string>;
            managedIdentity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AzureDNSManagedIdentity>;
            /**
             * resource group the DNS zone is located in
             */
            resourceGroupName?: pulumi.Input<string>;
            /**
             * ID of the Azure subscription
             */
            subscriptionID?: pulumi.Input<string>;
            /**
             * Auth: Azure Service Principal:
             * The TenantID of the Azure Service Principal used to authenticate with Azure DNS.
             * If set, ClientID and ClientSecret must also be set.
             */
            tenantID?: pulumi.Input<string>;
        }

        /**
         * Auth: Azure Service Principal:
         * A reference to a Secret containing the password associated with the Service Principal.
         * If set, ClientID and TenantID must also be set.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AzureDNSClientSecretSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Auth: Azure Service Principal:
         * A reference to a Secret containing the password associated with the Service Principal.
         * If set, ClientID and TenantID must also be set.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AzureDNSClientSecretSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Auth: Azure Workload Identity or Azure Managed Service Identity:
         * Settings to enable Azure Workload Identity or Azure Managed Service Identity
         * If set, ClientID, ClientSecret and TenantID must not be set.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AzureDNSManagedIdentity {
            /**
             * client ID of the managed identity, can not be used at the same time as resourceID
             */
            clientID?: pulumi.Input<string>;
            /**
             * resource ID of the managed identity, can not be used at the same time as clientID
             * Cannot be used for Azure Managed Service Identity
             */
            resourceID?: pulumi.Input<string>;
            /**
             * tenant ID of the managed identity, can not be used at the same time as resourceID
             */
            tenantID?: pulumi.Input<string>;
        }

        /**
         * Auth: Azure Workload Identity or Azure Managed Service Identity:
         * Settings to enable Azure Workload Identity or Azure Managed Service Identity
         * If set, ClientID, ClientSecret and TenantID must not be set.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AzureDNSManagedIdentityPatch {
            /**
             * client ID of the managed identity, can not be used at the same time as resourceID
             */
            clientID?: pulumi.Input<string>;
            /**
             * resource ID of the managed identity, can not be used at the same time as clientID
             * Cannot be used for Azure Managed Service Identity
             */
            resourceID?: pulumi.Input<string>;
            /**
             * tenant ID of the managed identity, can not be used at the same time as resourceID
             */
            tenantID?: pulumi.Input<string>;
        }

        /**
         * Use the Microsoft Azure DNS API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01AzureDNSPatch {
            /**
             * Auth: Azure Service Principal:
             * The ClientID of the Azure Service Principal used to authenticate with Azure DNS.
             * If set, ClientSecret and TenantID must also be set.
             */
            clientID?: pulumi.Input<string>;
            clientSecretSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AzureDNSClientSecretSecretRefPatch>;
            /**
             * name of the Azure environment (default AzurePublicCloud)
             */
            environment?: pulumi.Input<string>;
            /**
             * name of the DNS zone that should be used
             */
            hostedZoneName?: pulumi.Input<string>;
            managedIdentity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AzureDNSManagedIdentityPatch>;
            /**
             * resource group the DNS zone is located in
             */
            resourceGroupName?: pulumi.Input<string>;
            /**
             * ID of the Azure subscription
             */
            subscriptionID?: pulumi.Input<string>;
            /**
             * Auth: Azure Service Principal:
             * The TenantID of the Azure Service Principal used to authenticate with Azure DNS.
             * If set, ClientID and ClientSecret must also be set.
             */
            tenantID?: pulumi.Input<string>;
        }

        /**
         * Use the Google Cloud DNS API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudDNS {
            /**
             * HostedZoneName is an optional field that tells cert-manager in which
             * Cloud DNS zone the challenge record has to be created.
             * If left empty cert-manager will automatically choose a zone.
             */
            hostedZoneName?: pulumi.Input<string>;
            project?: pulumi.Input<string>;
            serviceAccountSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudDNSServiceAccountSecretRef>;
        }

        /**
         * Use the Google Cloud DNS API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudDNSPatch {
            /**
             * HostedZoneName is an optional field that tells cert-manager in which
             * Cloud DNS zone the challenge record has to be created.
             * If left empty cert-manager will automatically choose a zone.
             */
            hostedZoneName?: pulumi.Input<string>;
            project?: pulumi.Input<string>;
            serviceAccountSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudDNSServiceAccountSecretRefPatch>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudDNSServiceAccountSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudDNSServiceAccountSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Use the Cloudflare API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Cloudflare {
            apiKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudflareApiKeySecretRef>;
            apiTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudflareApiTokenSecretRef>;
            /**
             * Email of the account, only required when using API key based authentication.
             */
            email?: pulumi.Input<string>;
        }

        /**
         * API key to use to authenticate with Cloudflare.
         * Note: using an API token to authenticate is now the recommended method
         * as it allows greater control of permissions.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudflareApiKeySecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * API key to use to authenticate with Cloudflare.
         * Note: using an API token to authenticate is now the recommended method
         * as it allows greater control of permissions.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudflareApiKeySecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * API token used to authenticate with Cloudflare.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudflareApiTokenSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * API token used to authenticate with Cloudflare.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudflareApiTokenSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Use the Cloudflare API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01CloudflarePatch {
            apiKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudflareApiKeySecretRefPatch>;
            apiTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudflareApiTokenSecretRefPatch>;
            /**
             * Email of the account, only required when using API key based authentication.
             */
            email?: pulumi.Input<string>;
        }

        /**
         * Use the DigitalOcean DNS API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Digitalocean {
            tokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01DigitaloceanTokenSecretRef>;
        }

        /**
         * Use the DigitalOcean DNS API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01DigitaloceanPatch {
            tokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01DigitaloceanTokenSecretRefPatch>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01DigitaloceanTokenSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a specific 'key' within a Secret resource.
         * In some instances, `key` is a required field.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01DigitaloceanTokenSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Configures cert-manager to attempt to complete authorizations by
         * performing the DNS01 challenge flow.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Patch {
            acmeDNS?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AcmeDNSPatch>;
            akamai?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AkamaiPatch>;
            azureDNS?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01AzureDNSPatch>;
            cloudDNS?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudDNSPatch>;
            cloudflare?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01CloudflarePatch>;
            /**
             * CNAMEStrategy configures how the DNS01 provider should handle CNAME
             * records when found in DNS zones.
             */
            cnameStrategy?: pulumi.Input<string>;
            digitalocean?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01DigitaloceanPatch>;
            rfc2136?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Rfc2136Patch>;
            route53?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53Patch>;
            webhook?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01WebhookPatch>;
        }

        /**
         * Use RFC2136 ("Dynamic Updates in the Domain Name System") (https://datatracker.ietf.org/doc/rfc2136/)
         * to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Rfc2136 {
            /**
             * The IP address or hostname of an authoritative DNS server supporting
             * RFC2136 in the form host:port. If the host is an IPv6 address it must be
             * enclosed in square brackets (e.g [2001:db8::1]) ; port is optional.
             * This field is required.
             */
            nameserver?: pulumi.Input<string>;
            /**
             * The TSIG Algorithm configured in the DNS supporting RFC2136. Used only
             * when ``tsigSecretSecretRef`` and ``tsigKeyName`` are defined.
             * Supported values are (case-insensitive): ``HMACMD5`` (default),
             * ``HMACSHA1``, ``HMACSHA256`` or ``HMACSHA512``.
             */
            tsigAlgorithm?: pulumi.Input<string>;
            /**
             * The TSIG Key name configured in the DNS.
             * If ``tsigSecretSecretRef`` is defined, this field is required.
             */
            tsigKeyName?: pulumi.Input<string>;
            tsigSecretSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Rfc2136TsigSecretSecretRef>;
        }

        /**
         * Use RFC2136 ("Dynamic Updates in the Domain Name System") (https://datatracker.ietf.org/doc/rfc2136/)
         * to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Rfc2136Patch {
            /**
             * The IP address or hostname of an authoritative DNS server supporting
             * RFC2136 in the form host:port. If the host is an IPv6 address it must be
             * enclosed in square brackets (e.g [2001:db8::1]) ; port is optional.
             * This field is required.
             */
            nameserver?: pulumi.Input<string>;
            /**
             * The TSIG Algorithm configured in the DNS supporting RFC2136. Used only
             * when ``tsigSecretSecretRef`` and ``tsigKeyName`` are defined.
             * Supported values are (case-insensitive): ``HMACMD5`` (default),
             * ``HMACSHA1``, ``HMACSHA256`` or ``HMACSHA512``.
             */
            tsigAlgorithm?: pulumi.Input<string>;
            /**
             * The TSIG Key name configured in the DNS.
             * If ``tsigSecretSecretRef`` is defined, this field is required.
             */
            tsigKeyName?: pulumi.Input<string>;
            tsigSecretSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Rfc2136TsigSecretSecretRefPatch>;
        }

        /**
         * The name of the secret containing the TSIG value.
         * If ``tsigKeyName`` is defined, this field is required.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Rfc2136TsigSecretSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * The name of the secret containing the TSIG value.
         * If ``tsigKeyName`` is defined, this field is required.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Rfc2136TsigSecretSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Use the AWS Route53 API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53 {
            /**
             * The AccessKeyID is used for authentication.
             * Cannot be set when SecretAccessKeyID is set.
             * If neither the Access Key nor Key ID are set, we fall-back to using env
             * vars, shared credentials file or AWS Instance metadata,
             * see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
             */
            accessKeyID?: pulumi.Input<string>;
            accessKeyIDSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53AccessKeyIDSecretRef>;
            auth?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53Auth>;
            /**
             * If set, the provider will manage only this zone in Route53 and will not do a lookup using the route53:ListHostedZonesByName api call.
             */
            hostedZoneID?: pulumi.Input<string>;
            /**
             * Override the AWS region.
             *
             * Route53 is a global service and does not have regional endpoints but the
             * region specified here (or via environment variables) is used as a hint to
             * help compute the correct AWS credential scope and partition when it
             * connects to Route53. See:
             * - [Amazon Route 53 endpoints and quotas](https://docs.aws.amazon.com/general/latest/gr/r53.html)
             * - [Global services](https://docs.aws.amazon.com/whitepapers/latest/aws-fault-isolation-boundaries/global-services.html)
             *
             * If you omit this region field, cert-manager will use the region from
             * AWS_REGION and AWS_DEFAULT_REGION environment variables, if they are set
             * in the cert-manager controller Pod.
             *
             * The `region` field is not needed if you use [IAM Roles for Service Accounts (IRSA)](https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html).
             * Instead an AWS_REGION environment variable is added to the cert-manager controller Pod by:
             * [Amazon EKS Pod Identity Webhook](https://github.com/aws/amazon-eks-pod-identity-webhook).
             * In this case this `region` field value is ignored.
             *
             * The `region` field is not needed if you use [EKS Pod Identities](https://docs.aws.amazon.com/eks/latest/userguide/pod-identities.html).
             * Instead an AWS_REGION environment variable is added to the cert-manager controller Pod by:
             * [Amazon EKS Pod Identity Agent](https://github.com/aws/eks-pod-identity-agent),
             * In this case this `region` field value is ignored.
             */
            region?: pulumi.Input<string>;
            /**
             * Role is a Role ARN which the Route53 provider will assume using either the explicit credentials AccessKeyID/SecretAccessKey
             * or the inferred credentials from environment variables, shared credentials file or AWS Instance metadata
             */
            role?: pulumi.Input<string>;
            secretAccessKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53SecretAccessKeySecretRef>;
        }

        /**
         * The SecretAccessKey is used for authentication. If set, pull the AWS
         * access key ID from a key within a Kubernetes Secret.
         * Cannot be set when AccessKeyID is set.
         * If neither the Access Key nor Key ID are set, we fall-back to using env
         * vars, shared credentials file or AWS Instance metadata,
         * see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53AccessKeyIDSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * The SecretAccessKey is used for authentication. If set, pull the AWS
         * access key ID from a key within a Kubernetes Secret.
         * Cannot be set when AccessKeyID is set.
         * If neither the Access Key nor Key ID are set, we fall-back to using env
         * vars, shared credentials file or AWS Instance metadata,
         * see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53AccessKeyIDSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Auth configures how cert-manager authenticates.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53Auth {
            kubernetes?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetes>;
        }

        /**
         * Kubernetes authenticates with Route53 using AssumeRoleWithWebIdentity
         * by passing a bound ServiceAccount token.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetes {
            serviceAccountRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetesServiceAccountRef>;
        }

        /**
         * Kubernetes authenticates with Route53 using AssumeRoleWithWebIdentity
         * by passing a bound ServiceAccount token.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetesPatch {
            serviceAccountRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetesServiceAccountRefPatch>;
        }

        /**
         * A reference to a service account that will be used to request a bound
         * token (also known as "projected token"). To use this field, you must
         * configure an RBAC rule to let cert-manager request a token.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetesServiceAccountRef {
            /**
             * TokenAudiences is an optional list of audiences to include in the
             * token passed to AWS. The default token consisting of the issuer's namespace
             * and name is always included.
             * If unset the audience defaults to `sts.amazonaws.com`.
             */
            audiences?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Name of the ServiceAccount used to request a token.
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a service account that will be used to request a bound
         * token (also known as "projected token"). To use this field, you must
         * configure an RBAC rule to let cert-manager request a token.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetesServiceAccountRefPatch {
            /**
             * TokenAudiences is an optional list of audiences to include in the
             * token passed to AWS. The default token consisting of the issuer's namespace
             * and name is always included.
             * If unset the audience defaults to `sts.amazonaws.com`.
             */
            audiences?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Name of the ServiceAccount used to request a token.
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Auth configures how cert-manager authenticates.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53AuthPatch {
            kubernetes?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53AuthKubernetesPatch>;
        }

        /**
         * Use the AWS Route53 API to manage DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53Patch {
            /**
             * The AccessKeyID is used for authentication.
             * Cannot be set when SecretAccessKeyID is set.
             * If neither the Access Key nor Key ID are set, we fall-back to using env
             * vars, shared credentials file or AWS Instance metadata,
             * see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
             */
            accessKeyID?: pulumi.Input<string>;
            accessKeyIDSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53AccessKeyIDSecretRefPatch>;
            auth?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53AuthPatch>;
            /**
             * If set, the provider will manage only this zone in Route53 and will not do a lookup using the route53:ListHostedZonesByName api call.
             */
            hostedZoneID?: pulumi.Input<string>;
            /**
             * Override the AWS region.
             *
             * Route53 is a global service and does not have regional endpoints but the
             * region specified here (or via environment variables) is used as a hint to
             * help compute the correct AWS credential scope and partition when it
             * connects to Route53. See:
             * - [Amazon Route 53 endpoints and quotas](https://docs.aws.amazon.com/general/latest/gr/r53.html)
             * - [Global services](https://docs.aws.amazon.com/whitepapers/latest/aws-fault-isolation-boundaries/global-services.html)
             *
             * If you omit this region field, cert-manager will use the region from
             * AWS_REGION and AWS_DEFAULT_REGION environment variables, if they are set
             * in the cert-manager controller Pod.
             *
             * The `region` field is not needed if you use [IAM Roles for Service Accounts (IRSA)](https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html).
             * Instead an AWS_REGION environment variable is added to the cert-manager controller Pod by:
             * [Amazon EKS Pod Identity Webhook](https://github.com/aws/amazon-eks-pod-identity-webhook).
             * In this case this `region` field value is ignored.
             *
             * The `region` field is not needed if you use [EKS Pod Identities](https://docs.aws.amazon.com/eks/latest/userguide/pod-identities.html).
             * Instead an AWS_REGION environment variable is added to the cert-manager controller Pod by:
             * [Amazon EKS Pod Identity Agent](https://github.com/aws/eks-pod-identity-agent),
             * In this case this `region` field value is ignored.
             */
            region?: pulumi.Input<string>;
            /**
             * Role is a Role ARN which the Route53 provider will assume using either the explicit credentials AccessKeyID/SecretAccessKey
             * or the inferred credentials from environment variables, shared credentials file or AWS Instance metadata
             */
            role?: pulumi.Input<string>;
            secretAccessKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Route53SecretAccessKeySecretRefPatch>;
        }

        /**
         * The SecretAccessKey is used for authentication.
         * If neither the Access Key nor Key ID are set, we fall-back to using env
         * vars, shared credentials file or AWS Instance metadata,
         * see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53SecretAccessKeySecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * The SecretAccessKey is used for authentication.
         * If neither the Access Key nor Key ID are set, we fall-back to using env
         * vars, shared credentials file or AWS Instance metadata,
         * see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Route53SecretAccessKeySecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Configure an external webhook based DNS01 challenge solver to manage
         * DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01Webhook {
            /**
             * Additional configuration that should be passed to the webhook apiserver
             * when challenges are processed.
             * This can contain arbitrary JSON data.
             * Secret values should not be specified in this stanza.
             * If secret values are needed (e.g. credentials for a DNS service), you
             * should use a SecretKeySelector to reference a Secret resource.
             * For details on the schema of this field, consult the webhook provider
             * implementation's documentation.
             */
            config?: pulumi.Input<{[key: string]: any}>;
            /**
             * The API group name that should be used when POSTing ChallengePayload
             * resources to the webhook apiserver.
             * This should be the same as the GroupName specified in the webhook
             * provider implementation.
             */
            groupName?: pulumi.Input<string>;
            /**
             * The name of the solver to use, as defined in the webhook provider
             * implementation.
             * This will typically be the name of the provider, e.g. 'cloudflare'.
             */
            solverName?: pulumi.Input<string>;
        }

        /**
         * Configure an external webhook based DNS01 challenge solver to manage
         * DNS01 challenge records.
         */
        export interface ClusterIssuerSpecAcmeSolversDns01WebhookPatch {
            /**
             * Additional configuration that should be passed to the webhook apiserver
             * when challenges are processed.
             * This can contain arbitrary JSON data.
             * Secret values should not be specified in this stanza.
             * If secret values are needed (e.g. credentials for a DNS service), you
             * should use a SecretKeySelector to reference a Secret resource.
             * For details on the schema of this field, consult the webhook provider
             * implementation's documentation.
             */
            config?: pulumi.Input<{[key: string]: any}>;
            /**
             * The API group name that should be used when POSTing ChallengePayload
             * resources to the webhook apiserver.
             * This should be the same as the GroupName specified in the webhook
             * provider implementation.
             */
            groupName?: pulumi.Input<string>;
            /**
             * The name of the solver to use, as defined in the webhook provider
             * implementation.
             * This will typically be the name of the provider, e.g. 'cloudflare'.
             */
            solverName?: pulumi.Input<string>;
        }

        /**
         * Configures cert-manager to attempt to complete authorizations by
         * performing the HTTP01 challenge flow.
         * It is not possible to obtain certificates for wildcard domain names
         * (e.g. `*.example.com`) using the HTTP01 challenge mechanism.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01 {
            gatewayHTTPRoute?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoute>;
            ingress?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01Ingress>;
        }

        /**
         * The Gateway API is a sig-network community API that models service networking
         * in Kubernetes (https://gateway-api.sigs.k8s.io/). The Gateway solver will
         * create HTTPRoutes with the specified labels in the same namespace as the challenge.
         * This solver is experimental, and fields / behaviour may change in the future.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoute {
            /**
             * Custom labels that will be applied to HTTPRoutes created by cert-manager
             * while solving HTTP-01 challenges.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * When solving an HTTP-01 challenge, cert-manager creates an HTTPRoute.
             * cert-manager needs to know which parentRefs should be used when creating
             * the HTTPRoute. Usually, the parentRef references a Gateway. See:
             * https://gateway-api.sigs.k8s.io/api-types/httproute/#attaching-to-gateways
             */
            parentRefs?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRouteParentRefs>[]>;
            podTemplate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplate>;
            /**
             * Optional service type for Kubernetes solver service. Supported values
             * are NodePort or ClusterIP. If unset, defaults to NodePort.
             */
            serviceType?: pulumi.Input<string>;
        }

        /**
         * ParentReference identifies an API object (usually a Gateway) that can be considered
         * a parent of this resource (usually a route). There are two kinds of parent resources
         * with "Core" support:
         *
         * * Gateway (Gateway conformance profile)
         * * Service (Mesh conformance profile, ClusterIP Services only)
         *
         * This API may be extended in the future to support additional kinds of parent
         * resources.
         *
         * The API object must be valid in the cluster; the Group and Kind must
         * be registered in the cluster for this reference to be valid.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRouteParentRefs {
            /**
             * Group is the group of the referent.
             * When unspecified, "gateway.networking.k8s.io" is inferred.
             * To set the core API group (such as for a "Service" kind referent),
             * Group must be explicitly set to "" (empty string).
             *
             * Support: Core
             */
            group?: pulumi.Input<string>;
            /**
             * Kind is kind of the referent.
             *
             * There are two kinds of parent resources with "Core" support:
             *
             * * Gateway (Gateway conformance profile)
             * * Service (Mesh conformance profile, ClusterIP Services only)
             *
             * Support for other resources is Implementation-Specific.
             */
            kind?: pulumi.Input<string>;
            /**
             * Name is the name of the referent.
             *
             * Support: Core
             */
            name?: pulumi.Input<string>;
            /**
             * Namespace is the namespace of the referent. When unspecified, this refers
             * to the local namespace of the Route.
             *
             * Note that there are specific rules for ParentRefs which cross namespace
             * boundaries. Cross-namespace references are only valid if they are explicitly
             * allowed by something in the namespace they are referring to. For example:
             * Gateway has the AllowedRoutes field, and ReferenceGrant provides a
             * generic way to enable any other kind of cross-namespace reference.
             *
             * <gateway:experimental:description>
             * ParentRefs from a Route to a Service in the same namespace are "producer"
             * routes, which apply default routing rules to inbound connections from
             * any namespace to the Service.
             *
             * ParentRefs from a Route to a Service in a different namespace are
             * "consumer" routes, and these routing rules are only applied to outbound
             * connections originating from the same namespace as the Route, for which
             * the intended destination of the connections are a Service targeted as a
             * ParentRef of the Route.
             * </gateway:experimental:description>
             *
             * Support: Core
             */
            namespace?: pulumi.Input<string>;
            /**
             * Port is the network port this Route targets. It can be interpreted
             * differently based on the type of parent resource.
             *
             * When the parent resource is a Gateway, this targets all listeners
             * listening on the specified port that also support this kind of Route(and
             * select this Route). It's not recommended to set `Port` unless the
             * networking behaviors specified in a Route must apply to a specific port
             * as opposed to a listener(s) whose port(s) may be changed. When both Port
             * and SectionName are specified, the name and port of the selected listener
             * must match both specified values.
             *
             * <gateway:experimental:description>
             * When the parent resource is a Service, this targets a specific port in the
             * Service spec. When both Port (experimental) and SectionName are specified,
             * the name and port of the selected port must match both specified values.
             * </gateway:experimental:description>
             *
             * Implementations MAY choose to support other parent resources.
             * Implementations supporting other types of parent resources MUST clearly
             * document how/if Port is interpreted.
             *
             * For the purpose of status, an attachment is considered successful as
             * long as the parent resource accepts it partially. For example, Gateway
             * listeners can restrict which Routes can attach to them by Route kind,
             * namespace, or hostname. If 1 of 2 Gateway listeners accept attachment
             * from the referencing Route, the Route MUST be considered successfully
             * attached. If no Gateway listeners accept attachment from this Route,
             * the Route MUST be considered detached from the Gateway.
             *
             * Support: Extended
             */
            port?: pulumi.Input<number>;
            /**
             * SectionName is the name of a section within the target resource. In the
             * following resources, SectionName is interpreted as the following:
             *
             * * Gateway: Listener name. When both Port (experimental) and SectionName
             * are specified, the name and port of the selected listener must match
             * both specified values.
             * * Service: Port name. When both Port (experimental) and SectionName
             * are specified, the name and port of the selected listener must match
             * both specified values.
             *
             * Implementations MAY choose to support attaching Routes to other resources.
             * If that is the case, they MUST clearly document how SectionName is
             * interpreted.
             *
             * When unspecified (empty string), this will reference the entire resource.
             * For the purpose of status, an attachment is considered successful if at
             * least one section in the parent resource accepts it. For example, Gateway
             * listeners can restrict which Routes can attach to them by Route kind,
             * namespace, or hostname. If 1 of 2 Gateway listeners accept attachment from
             * the referencing Route, the Route MUST be considered successfully
             * attached. If no Gateway listeners accept attachment from this Route, the
             * Route MUST be considered detached from the Gateway.
             *
             * Support: Core
             */
            sectionName?: pulumi.Input<string>;
        }

        /**
         * ParentReference identifies an API object (usually a Gateway) that can be considered
         * a parent of this resource (usually a route). There are two kinds of parent resources
         * with "Core" support:
         *
         * * Gateway (Gateway conformance profile)
         * * Service (Mesh conformance profile, ClusterIP Services only)
         *
         * This API may be extended in the future to support additional kinds of parent
         * resources.
         *
         * The API object must be valid in the cluster; the Group and Kind must
         * be registered in the cluster for this reference to be valid.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRouteParentRefsPatch {
            /**
             * Group is the group of the referent.
             * When unspecified, "gateway.networking.k8s.io" is inferred.
             * To set the core API group (such as for a "Service" kind referent),
             * Group must be explicitly set to "" (empty string).
             *
             * Support: Core
             */
            group?: pulumi.Input<string>;
            /**
             * Kind is kind of the referent.
             *
             * There are two kinds of parent resources with "Core" support:
             *
             * * Gateway (Gateway conformance profile)
             * * Service (Mesh conformance profile, ClusterIP Services only)
             *
             * Support for other resources is Implementation-Specific.
             */
            kind?: pulumi.Input<string>;
            /**
             * Name is the name of the referent.
             *
             * Support: Core
             */
            name?: pulumi.Input<string>;
            /**
             * Namespace is the namespace of the referent. When unspecified, this refers
             * to the local namespace of the Route.
             *
             * Note that there are specific rules for ParentRefs which cross namespace
             * boundaries. Cross-namespace references are only valid if they are explicitly
             * allowed by something in the namespace they are referring to. For example:
             * Gateway has the AllowedRoutes field, and ReferenceGrant provides a
             * generic way to enable any other kind of cross-namespace reference.
             *
             * <gateway:experimental:description>
             * ParentRefs from a Route to a Service in the same namespace are "producer"
             * routes, which apply default routing rules to inbound connections from
             * any namespace to the Service.
             *
             * ParentRefs from a Route to a Service in a different namespace are
             * "consumer" routes, and these routing rules are only applied to outbound
             * connections originating from the same namespace as the Route, for which
             * the intended destination of the connections are a Service targeted as a
             * ParentRef of the Route.
             * </gateway:experimental:description>
             *
             * Support: Core
             */
            namespace?: pulumi.Input<string>;
            /**
             * Port is the network port this Route targets. It can be interpreted
             * differently based on the type of parent resource.
             *
             * When the parent resource is a Gateway, this targets all listeners
             * listening on the specified port that also support this kind of Route(and
             * select this Route). It's not recommended to set `Port` unless the
             * networking behaviors specified in a Route must apply to a specific port
             * as opposed to a listener(s) whose port(s) may be changed. When both Port
             * and SectionName are specified, the name and port of the selected listener
             * must match both specified values.
             *
             * <gateway:experimental:description>
             * When the parent resource is a Service, this targets a specific port in the
             * Service spec. When both Port (experimental) and SectionName are specified,
             * the name and port of the selected port must match both specified values.
             * </gateway:experimental:description>
             *
             * Implementations MAY choose to support other parent resources.
             * Implementations supporting other types of parent resources MUST clearly
             * document how/if Port is interpreted.
             *
             * For the purpose of status, an attachment is considered successful as
             * long as the parent resource accepts it partially. For example, Gateway
             * listeners can restrict which Routes can attach to them by Route kind,
             * namespace, or hostname. If 1 of 2 Gateway listeners accept attachment
             * from the referencing Route, the Route MUST be considered successfully
             * attached. If no Gateway listeners accept attachment from this Route,
             * the Route MUST be considered detached from the Gateway.
             *
             * Support: Extended
             */
            port?: pulumi.Input<number>;
            /**
             * SectionName is the name of a section within the target resource. In the
             * following resources, SectionName is interpreted as the following:
             *
             * * Gateway: Listener name. When both Port (experimental) and SectionName
             * are specified, the name and port of the selected listener must match
             * both specified values.
             * * Service: Port name. When both Port (experimental) and SectionName
             * are specified, the name and port of the selected listener must match
             * both specified values.
             *
             * Implementations MAY choose to support attaching Routes to other resources.
             * If that is the case, they MUST clearly document how SectionName is
             * interpreted.
             *
             * When unspecified (empty string), this will reference the entire resource.
             * For the purpose of status, an attachment is considered successful if at
             * least one section in the parent resource accepts it. For example, Gateway
             * listeners can restrict which Routes can attach to them by Route kind,
             * namespace, or hostname. If 1 of 2 Gateway listeners accept attachment from
             * the referencing Route, the Route MUST be considered successfully
             * attached. If no Gateway listeners accept attachment from this Route, the
             * Route MUST be considered detached from the Gateway.
             *
             * Support: Core
             */
            sectionName?: pulumi.Input<string>;
        }

        /**
         * The Gateway API is a sig-network community API that models service networking
         * in Kubernetes (https://gateway-api.sigs.k8s.io/). The Gateway solver will
         * create HTTPRoutes with the specified labels in the same namespace as the challenge.
         * This solver is experimental, and fields / behaviour may change in the future.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePatch {
            /**
             * Custom labels that will be applied to HTTPRoutes created by cert-manager
             * while solving HTTP-01 challenges.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * When solving an HTTP-01 challenge, cert-manager creates an HTTPRoute.
             * cert-manager needs to know which parentRefs should be used when creating
             * the HTTPRoute. Usually, the parentRef references a Gateway. See:
             * https://gateway-api.sigs.k8s.io/api-types/httproute/#attaching-to-gateways
             */
            parentRefs?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRouteParentRefsPatch>[]>;
            podTemplate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplatePatch>;
            /**
             * Optional service type for Kubernetes solver service. Supported values
             * are NodePort or ClusterIP. If unset, defaults to NodePort.
             */
            serviceType?: pulumi.Input<string>;
        }

        /**
         * Optional pod template used to configure the ACME challenge solver pods
         * used for HTTP01 challenges.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplate {
            metadata?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateMetadata>;
            spec?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpec>;
        }

        /**
         * ObjectMeta overrides for the pod used to solve HTTP01 challenges.
         * Only the 'labels' and 'annotations' fields may be set.
         * If labels or annotations overlap with in-built values, the values here
         * will override the in-built values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateMetadata {
            /**
             * Annotations that should be added to the created ACME HTTP01 solver pods.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels that should be added to the created ACME HTTP01 solver pods.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * ObjectMeta overrides for the pod used to solve HTTP01 challenges.
         * Only the 'labels' and 'annotations' fields may be set.
         * If labels or annotations overlap with in-built values, the values here
         * will override the in-built values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateMetadataPatch {
            /**
             * Annotations that should be added to the created ACME HTTP01 solver pods.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels that should be added to the created ACME HTTP01 solver pods.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Optional pod template used to configure the ACME challenge solver pods
         * used for HTTP01 challenges.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplatePatch {
            metadata?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateMetadataPatch>;
            spec?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecPatch>;
        }

        /**
         * PodSpec defines overrides for the HTTP01 challenge solver pod.
         * Check ACMEChallengeSolverHTTP01IngressPodSpec to find out currently supported fields.
         * All other fields will be ignored.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpec {
            affinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinity>;
            /**
             * If specified, the pod's imagePullSecrets
             */
            imagePullSecrets?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecImagePullSecrets>[]>;
            /**
             * NodeSelector is a selector which must be true for the pod to fit on a node.
             * Selector which must match a node's labels for the pod to be scheduled on that node.
             * More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
             */
            nodeSelector?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * If specified, the pod's priorityClassName.
             */
            priorityClassName?: pulumi.Input<string>;
            securityContext?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContext>;
            /**
             * If specified, the pod's service account
             */
            serviceAccountName?: pulumi.Input<string>;
            /**
             * If specified, the pod's tolerations.
             */
            tolerations?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecTolerations>[]>;
        }

        /**
         * If specified, the pod's scheduling constraints
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinity {
            nodeAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinity>;
            podAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinity>;
            podAntiAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinity>;
        }

        /**
         * Describes node affinity scheduling rules for the pod.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinity {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node matches the corresponding matchExpressions; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecution>[]>;
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecution>;
        }

        /**
         * Describes node affinity scheduling rules for the pod.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPatch {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node matches the corresponding matchExpressions; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch>;
        }

        /**
         * An empty preferred scheduling term matches all objects with implicit weight 0
         * (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecution {
            preference?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreference>;
            /**
             * Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * An empty preferred scheduling term matches all objects with implicit weight 0
         * (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch {
            preference?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferencePatch>;
            /**
             * Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * A node selector term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreference {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressions>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFields>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressions {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressionsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFields {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFieldsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferencePatch {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressionsPatch>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFieldsPatch>[]>;
        }

        /**
         * If the affinity requirements specified by this field are not met at
         * scheduling time, the pod will not be scheduled onto the node.
         * If the affinity requirements specified by this field cease to be met
         * at some point during pod execution (e.g. due to an update), the system
         * may or may not try to eventually evict the pod from its node.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecution {
            /**
             * Required. A list of node selector terms. The terms are ORed.
             */
            nodeSelectorTerms?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTerms>[]>;
        }

        /**
         * A null or empty node selector term matches no objects. The requirements of
         * them are ANDed.
         * The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTerms {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressions>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFields>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressions {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressionsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFields {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFieldsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A null or empty node selector term matches no objects. The requirements of
         * them are ANDed.
         * The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsPatch {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressionsPatch>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFieldsPatch>[]>;
        }

        /**
         * If the affinity requirements specified by this field are not met at
         * scheduling time, the pod will not be scheduled onto the node.
         * If the affinity requirements specified by this field cease to be met
         * at some point during pod execution (e.g. due to an update), the system
         * may or may not try to eventually evict the pod from its node.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch {
            /**
             * Required. A list of node selector terms. The terms are ORed.
             */
            nodeSelectorTerms?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsPatch>[]>;
        }

        /**
         * If specified, the pod's scheduling constraints
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPatch {
            nodeAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityNodeAffinityPatch>;
            podAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPatch>;
            podAntiAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPatch>;
        }

        /**
         * Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinity {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecution>[]>;
            /**
             * If the affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecution>[]>;
        }

        /**
         * Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPatch {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
            /**
             * If the affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecution {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecution {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinity {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the anti-affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling anti-affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecution>[]>;
            /**
             * If the anti-affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the anti-affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecution>[]>;
        }

        /**
         * Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPatch {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the anti-affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling anti-affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
            /**
             * If the anti-affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the anti-affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecution {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecution {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * LocalObjectReference contains enough information to let you locate the
         * referenced object inside the same namespace.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecImagePullSecrets {
            /**
             * Name of the referent.
             * This field is effectively required, but due to backwards compatibility is
             * allowed to be empty. Instances of this type with an empty value here are
             * almost certainly wrong.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * LocalObjectReference contains enough information to let you locate the
         * referenced object inside the same namespace.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecImagePullSecretsPatch {
            /**
             * Name of the referent.
             * This field is effectively required, but due to backwards compatibility is
             * allowed to be empty. Instances of this type with an empty value here are
             * almost certainly wrong.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * PodSpec defines overrides for the HTTP01 challenge solver pod.
         * Check ACMEChallengeSolverHTTP01IngressPodSpec to find out currently supported fields.
         * All other fields will be ignored.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecPatch {
            affinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecAffinityPatch>;
            /**
             * If specified, the pod's imagePullSecrets
             */
            imagePullSecrets?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecImagePullSecretsPatch>[]>;
            /**
             * NodeSelector is a selector which must be true for the pod to fit on a node.
             * Selector which must match a node's labels for the pod to be scheduled on that node.
             * More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
             */
            nodeSelector?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * If specified, the pod's priorityClassName.
             */
            priorityClassName?: pulumi.Input<string>;
            securityContext?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextPatch>;
            /**
             * If specified, the pod's service account
             */
            serviceAccountName?: pulumi.Input<string>;
            /**
             * If specified, the pod's tolerations.
             */
            tolerations?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecTolerationsPatch>[]>;
        }

        /**
         * If specified, the pod's security context
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContext {
            /**
             * A special supplemental group that applies to all containers in a pod.
             * Some volume types allow the Kubelet to change the ownership of that volume
             * to be owned by the pod:
             *
             * 1. The owning GID will be the FSGroup
             * 2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
             * 3. The permission bits are OR'd with rw-rw----
             *
             * If unset, the Kubelet will not modify the ownership and permissions of any volume.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroup?: pulumi.Input<number>;
            /**
             * fsGroupChangePolicy defines behavior of changing ownership and permission of the volume
             * before being exposed inside Pod. This field will only apply to
             * volume types which support fsGroup based ownership(and permissions).
             * It will have no effect on ephemeral volume types such as: secret, configmaps
             * and emptydir.
             * Valid values are "OnRootMismatch" and "Always". If not specified, "Always" is used.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroupChangePolicy?: pulumi.Input<string>;
            /**
             * The GID to run the entrypoint of the container process.
             * Uses runtime default if unset.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsGroup?: pulumi.Input<number>;
            /**
             * Indicates that the container must run as a non-root user.
             * If true, the Kubelet will validate the image at runtime to ensure that it
             * does not run as UID 0 (root) and fail to start the container if it does.
             * If unset or false, no such validation will be performed.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence.
             */
            runAsNonRoot?: pulumi.Input<boolean>;
            /**
             * The UID to run the entrypoint of the container process.
             * Defaults to user specified in image metadata if unspecified.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsUser?: pulumi.Input<number>;
            seLinuxOptions?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeLinuxOptions>;
            seccompProfile?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeccompProfile>;
            /**
             * A list of groups applied to the first process run in each container, in addition
             * to the container's primary GID, the fsGroup (if specified), and group memberships
             * defined in the container image for the uid of the container process. If unspecified,
             * no additional groups are added to any container. Note that group memberships
             * defined in the container image for the uid of the container process are still effective,
             * even if they are not included in this list.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            supplementalGroups?: pulumi.Input<pulumi.Input<number>[]>;
            /**
             * Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
             * sysctls (by the container runtime) might fail to launch.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            sysctls?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSysctls>[]>;
        }

        /**
         * If specified, the pod's security context
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextPatch {
            /**
             * A special supplemental group that applies to all containers in a pod.
             * Some volume types allow the Kubelet to change the ownership of that volume
             * to be owned by the pod:
             *
             * 1. The owning GID will be the FSGroup
             * 2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
             * 3. The permission bits are OR'd with rw-rw----
             *
             * If unset, the Kubelet will not modify the ownership and permissions of any volume.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroup?: pulumi.Input<number>;
            /**
             * fsGroupChangePolicy defines behavior of changing ownership and permission of the volume
             * before being exposed inside Pod. This field will only apply to
             * volume types which support fsGroup based ownership(and permissions).
             * It will have no effect on ephemeral volume types such as: secret, configmaps
             * and emptydir.
             * Valid values are "OnRootMismatch" and "Always". If not specified, "Always" is used.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroupChangePolicy?: pulumi.Input<string>;
            /**
             * The GID to run the entrypoint of the container process.
             * Uses runtime default if unset.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsGroup?: pulumi.Input<number>;
            /**
             * Indicates that the container must run as a non-root user.
             * If true, the Kubelet will validate the image at runtime to ensure that it
             * does not run as UID 0 (root) and fail to start the container if it does.
             * If unset or false, no such validation will be performed.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence.
             */
            runAsNonRoot?: pulumi.Input<boolean>;
            /**
             * The UID to run the entrypoint of the container process.
             * Defaults to user specified in image metadata if unspecified.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsUser?: pulumi.Input<number>;
            seLinuxOptions?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeLinuxOptionsPatch>;
            seccompProfile?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeccompProfilePatch>;
            /**
             * A list of groups applied to the first process run in each container, in addition
             * to the container's primary GID, the fsGroup (if specified), and group memberships
             * defined in the container image for the uid of the container process. If unspecified,
             * no additional groups are added to any container. Note that group memberships
             * defined in the container image for the uid of the container process are still effective,
             * even if they are not included in this list.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            supplementalGroups?: pulumi.Input<pulumi.Input<number>[]>;
            /**
             * Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
             * sysctls (by the container runtime) might fail to launch.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            sysctls?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSysctlsPatch>[]>;
        }

        /**
         * The SELinux context to be applied to all containers.
         * If unspecified, the container runtime will allocate a random SELinux context for each
         * container.  May also be set in SecurityContext.  If set in
         * both SecurityContext and PodSecurityContext, the value specified in SecurityContext
         * takes precedence for that container.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeLinuxOptions {
            /**
             * Level is SELinux level label that applies to the container.
             */
            level?: pulumi.Input<string>;
            /**
             * Role is a SELinux role label that applies to the container.
             */
            role?: pulumi.Input<string>;
            /**
             * Type is a SELinux type label that applies to the container.
             */
            type?: pulumi.Input<string>;
            /**
             * User is a SELinux user label that applies to the container.
             */
            user?: pulumi.Input<string>;
        }

        /**
         * The SELinux context to be applied to all containers.
         * If unspecified, the container runtime will allocate a random SELinux context for each
         * container.  May also be set in SecurityContext.  If set in
         * both SecurityContext and PodSecurityContext, the value specified in SecurityContext
         * takes precedence for that container.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeLinuxOptionsPatch {
            /**
             * Level is SELinux level label that applies to the container.
             */
            level?: pulumi.Input<string>;
            /**
             * Role is a SELinux role label that applies to the container.
             */
            role?: pulumi.Input<string>;
            /**
             * Type is a SELinux type label that applies to the container.
             */
            type?: pulumi.Input<string>;
            /**
             * User is a SELinux user label that applies to the container.
             */
            user?: pulumi.Input<string>;
        }

        /**
         * The seccomp options to use by the containers in this pod.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeccompProfile {
            /**
             * localhostProfile indicates a profile defined in a file on the node should be used.
             * The profile must be preconfigured on the node to work.
             * Must be a descending path, relative to the kubelet's configured seccomp profile location.
             * Must be set if type is "Localhost". Must NOT be set for any other type.
             */
            localhostProfile?: pulumi.Input<string>;
            /**
             * type indicates which kind of seccomp profile will be applied.
             * Valid options are:
             *
             * Localhost - a profile defined in a file on the node should be used.
             * RuntimeDefault - the container runtime default profile should be used.
             * Unconfined - no profile should be applied.
             */
            type?: pulumi.Input<string>;
        }

        /**
         * The seccomp options to use by the containers in this pod.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSeccompProfilePatch {
            /**
             * localhostProfile indicates a profile defined in a file on the node should be used.
             * The profile must be preconfigured on the node to work.
             * Must be a descending path, relative to the kubelet's configured seccomp profile location.
             * Must be set if type is "Localhost". Must NOT be set for any other type.
             */
            localhostProfile?: pulumi.Input<string>;
            /**
             * type indicates which kind of seccomp profile will be applied.
             * Valid options are:
             *
             * Localhost - a profile defined in a file on the node should be used.
             * RuntimeDefault - the container runtime default profile should be used.
             * Unconfined - no profile should be applied.
             */
            type?: pulumi.Input<string>;
        }

        /**
         * Sysctl defines a kernel parameter to be set
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSysctls {
            /**
             * Name of a property to set
             */
            name?: pulumi.Input<string>;
            /**
             * Value of a property to set
             */
            value?: pulumi.Input<string>;
        }

        /**
         * Sysctl defines a kernel parameter to be set
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecSecurityContextSysctlsPatch {
            /**
             * Name of a property to set
             */
            name?: pulumi.Input<string>;
            /**
             * Value of a property to set
             */
            value?: pulumi.Input<string>;
        }

        /**
         * The pod this Toleration is attached to tolerates any taint that matches
         * the triple <key,value,effect> using the matching operator <operator>.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecTolerations {
            /**
             * Effect indicates the taint effect to match. Empty means match all taint effects.
             * When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
             */
            effect?: pulumi.Input<string>;
            /**
             * Key is the taint key that the toleration applies to. Empty means match all taint keys.
             * If the key is empty, operator must be Exists; this combination means to match all values and all keys.
             */
            key?: pulumi.Input<string>;
            /**
             * Operator represents a key's relationship to the value.
             * Valid operators are Exists and Equal. Defaults to Equal.
             * Exists is equivalent to wildcard for value, so that a pod can
             * tolerate all taints of a particular category.
             */
            operator?: pulumi.Input<string>;
            /**
             * TolerationSeconds represents the period of time the toleration (which must be
             * of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
             * it is not set, which means tolerate the taint forever (do not evict). Zero and
             * negative values will be treated as 0 (evict immediately) by the system.
             */
            tolerationSeconds?: pulumi.Input<number>;
            /**
             * Value is the taint value the toleration matches to.
             * If the operator is Exists, the value should be empty, otherwise just a regular string.
             */
            value?: pulumi.Input<string>;
        }

        /**
         * The pod this Toleration is attached to tolerates any taint that matches
         * the triple <key,value,effect> using the matching operator <operator>.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePodTemplateSpecTolerationsPatch {
            /**
             * Effect indicates the taint effect to match. Empty means match all taint effects.
             * When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
             */
            effect?: pulumi.Input<string>;
            /**
             * Key is the taint key that the toleration applies to. Empty means match all taint keys.
             * If the key is empty, operator must be Exists; this combination means to match all values and all keys.
             */
            key?: pulumi.Input<string>;
            /**
             * Operator represents a key's relationship to the value.
             * Valid operators are Exists and Equal. Defaults to Equal.
             * Exists is equivalent to wildcard for value, so that a pod can
             * tolerate all taints of a particular category.
             */
            operator?: pulumi.Input<string>;
            /**
             * TolerationSeconds represents the period of time the toleration (which must be
             * of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
             * it is not set, which means tolerate the taint forever (do not evict). Zero and
             * negative values will be treated as 0 (evict immediately) by the system.
             */
            tolerationSeconds?: pulumi.Input<number>;
            /**
             * Value is the taint value the toleration matches to.
             * If the operator is Exists, the value should be empty, otherwise just a regular string.
             */
            value?: pulumi.Input<string>;
        }

        /**
         * The ingress based HTTP01 challenge solver will solve challenges by
         * creating or modifying Ingress resources in order to route requests for
         * '/.well-known/acme-challenge/XYZ' to 'challenge solver' pods that are
         * provisioned by cert-manager for each Challenge to be completed.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01Ingress {
            /**
             * This field configures the annotation `kubernetes.io/ingress.class` when
             * creating Ingress resources to solve ACME challenges that use this
             * challenge solver. Only one of `class`, `name` or `ingressClassName` may
             * be specified.
             */
            class?: pulumi.Input<string>;
            /**
             * This field configures the field `ingressClassName` on the created Ingress
             * resources used to solve ACME challenges that use this challenge solver.
             * This is the recommended way of configuring the ingress class. Only one of
             * `class`, `name` or `ingressClassName` may be specified.
             */
            ingressClassName?: pulumi.Input<string>;
            ingressTemplate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplate>;
            /**
             * The name of the ingress resource that should have ACME challenge solving
             * routes inserted into it in order to solve HTTP01 challenges.
             * This is typically used in conjunction with ingress controllers like
             * ingress-gce, which maintains a 1:1 mapping between external IPs and
             * ingress resources. Only one of `class`, `name` or `ingressClassName` may
             * be specified.
             */
            name?: pulumi.Input<string>;
            podTemplate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplate>;
            /**
             * Optional service type for Kubernetes solver service. Supported values
             * are NodePort or ClusterIP. If unset, defaults to NodePort.
             */
            serviceType?: pulumi.Input<string>;
        }

        /**
         * Optional ingress template used to configure the ACME challenge solver
         * ingress used for HTTP01 challenges.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplate {
            metadata?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplateMetadata>;
        }

        /**
         * ObjectMeta overrides for the ingress used to solve HTTP01 challenges.
         * Only the 'labels' and 'annotations' fields may be set.
         * If labels or annotations overlap with in-built values, the values here
         * will override the in-built values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplateMetadata {
            /**
             * Annotations that should be added to the created ACME HTTP01 solver ingress.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels that should be added to the created ACME HTTP01 solver ingress.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * ObjectMeta overrides for the ingress used to solve HTTP01 challenges.
         * Only the 'labels' and 'annotations' fields may be set.
         * If labels or annotations overlap with in-built values, the values here
         * will override the in-built values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplateMetadataPatch {
            /**
             * Annotations that should be added to the created ACME HTTP01 solver ingress.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels that should be added to the created ACME HTTP01 solver ingress.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Optional ingress template used to configure the ACME challenge solver
         * ingress used for HTTP01 challenges.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplatePatch {
            metadata?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplateMetadataPatch>;
        }

        /**
         * The ingress based HTTP01 challenge solver will solve challenges by
         * creating or modifying Ingress resources in order to route requests for
         * '/.well-known/acme-challenge/XYZ' to 'challenge solver' pods that are
         * provisioned by cert-manager for each Challenge to be completed.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPatch {
            /**
             * This field configures the annotation `kubernetes.io/ingress.class` when
             * creating Ingress resources to solve ACME challenges that use this
             * challenge solver. Only one of `class`, `name` or `ingressClassName` may
             * be specified.
             */
            class?: pulumi.Input<string>;
            /**
             * This field configures the field `ingressClassName` on the created Ingress
             * resources used to solve ACME challenges that use this challenge solver.
             * This is the recommended way of configuring the ingress class. Only one of
             * `class`, `name` or `ingressClassName` may be specified.
             */
            ingressClassName?: pulumi.Input<string>;
            ingressTemplate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressIngressTemplatePatch>;
            /**
             * The name of the ingress resource that should have ACME challenge solving
             * routes inserted into it in order to solve HTTP01 challenges.
             * This is typically used in conjunction with ingress controllers like
             * ingress-gce, which maintains a 1:1 mapping between external IPs and
             * ingress resources. Only one of `class`, `name` or `ingressClassName` may
             * be specified.
             */
            name?: pulumi.Input<string>;
            podTemplate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplatePatch>;
            /**
             * Optional service type for Kubernetes solver service. Supported values
             * are NodePort or ClusterIP. If unset, defaults to NodePort.
             */
            serviceType?: pulumi.Input<string>;
        }

        /**
         * Optional pod template used to configure the ACME challenge solver pods
         * used for HTTP01 challenges.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplate {
            metadata?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateMetadata>;
            spec?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpec>;
        }

        /**
         * ObjectMeta overrides for the pod used to solve HTTP01 challenges.
         * Only the 'labels' and 'annotations' fields may be set.
         * If labels or annotations overlap with in-built values, the values here
         * will override the in-built values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateMetadata {
            /**
             * Annotations that should be added to the created ACME HTTP01 solver pods.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels that should be added to the created ACME HTTP01 solver pods.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * ObjectMeta overrides for the pod used to solve HTTP01 challenges.
         * Only the 'labels' and 'annotations' fields may be set.
         * If labels or annotations overlap with in-built values, the values here
         * will override the in-built values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateMetadataPatch {
            /**
             * Annotations that should be added to the created ACME HTTP01 solver pods.
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * Labels that should be added to the created ACME HTTP01 solver pods.
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Optional pod template used to configure the ACME challenge solver pods
         * used for HTTP01 challenges.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplatePatch {
            metadata?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateMetadataPatch>;
            spec?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecPatch>;
        }

        /**
         * PodSpec defines overrides for the HTTP01 challenge solver pod.
         * Check ACMEChallengeSolverHTTP01IngressPodSpec to find out currently supported fields.
         * All other fields will be ignored.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpec {
            affinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinity>;
            /**
             * If specified, the pod's imagePullSecrets
             */
            imagePullSecrets?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecImagePullSecrets>[]>;
            /**
             * NodeSelector is a selector which must be true for the pod to fit on a node.
             * Selector which must match a node's labels for the pod to be scheduled on that node.
             * More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
             */
            nodeSelector?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * If specified, the pod's priorityClassName.
             */
            priorityClassName?: pulumi.Input<string>;
            securityContext?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContext>;
            /**
             * If specified, the pod's service account
             */
            serviceAccountName?: pulumi.Input<string>;
            /**
             * If specified, the pod's tolerations.
             */
            tolerations?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecTolerations>[]>;
        }

        /**
         * If specified, the pod's scheduling constraints
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinity {
            nodeAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinity>;
            podAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinity>;
            podAntiAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinity>;
        }

        /**
         * Describes node affinity scheduling rules for the pod.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinity {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node matches the corresponding matchExpressions; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecution>[]>;
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecution>;
        }

        /**
         * Describes node affinity scheduling rules for the pod.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPatch {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node matches the corresponding matchExpressions; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch>;
        }

        /**
         * An empty preferred scheduling term matches all objects with implicit weight 0
         * (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecution {
            preference?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreference>;
            /**
             * Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * An empty preferred scheduling term matches all objects with implicit weight 0
         * (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch {
            preference?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferencePatch>;
            /**
             * Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * A node selector term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreference {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressions>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFields>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressions {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressionsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFields {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFieldsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferencePatch {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchExpressionsPatch>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPreferredDuringSchedulingIgnoredDuringExecutionPreferenceMatchFieldsPatch>[]>;
        }

        /**
         * If the affinity requirements specified by this field are not met at
         * scheduling time, the pod will not be scheduled onto the node.
         * If the affinity requirements specified by this field cease to be met
         * at some point during pod execution (e.g. due to an update), the system
         * may or may not try to eventually evict the pod from its node.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecution {
            /**
             * Required. A list of node selector terms. The terms are ORed.
             */
            nodeSelectorTerms?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTerms>[]>;
        }

        /**
         * A null or empty node selector term matches no objects. The requirements of
         * them are ANDed.
         * The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTerms {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressions>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFields>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressions {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressionsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFields {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A node selector requirement is a selector that contains values, a key, and an operator
         * that relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFieldsPatch {
            /**
             * The label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * Represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
             */
            operator?: pulumi.Input<string>;
            /**
             * An array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. If the operator is Gt or Lt, the values
             * array must have a single element, which will be interpreted as an integer.
             * This array is replaced during a strategic merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A null or empty node selector term matches no objects. The requirements of
         * them are ANDed.
         * The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsPatch {
            /**
             * A list of node selector requirements by node's labels.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchExpressionsPatch>[]>;
            /**
             * A list of node selector requirements by node's fields.
             */
            matchFields?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsMatchFieldsPatch>[]>;
        }

        /**
         * If the affinity requirements specified by this field are not met at
         * scheduling time, the pod will not be scheduled onto the node.
         * If the affinity requirements specified by this field cease to be met
         * at some point during pod execution (e.g. due to an update), the system
         * may or may not try to eventually evict the pod from its node.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch {
            /**
             * Required. A list of node selector terms. The terms are ORed.
             */
            nodeSelectorTerms?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityRequiredDuringSchedulingIgnoredDuringExecutionNodeSelectorTermsPatch>[]>;
        }

        /**
         * If specified, the pod's scheduling constraints
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPatch {
            nodeAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityNodeAffinityPatch>;
            podAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPatch>;
            podAntiAffinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPatch>;
        }

        /**
         * Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinity {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecution>[]>;
            /**
             * If the affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecution>[]>;
        }

        /**
         * Describes pod affinity scheduling rules (e.g. co-locate this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPatch {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
            /**
             * If the affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecution {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecution {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinity {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the anti-affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling anti-affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecution>[]>;
            /**
             * If the anti-affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the anti-affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecution>[]>;
        }

        /**
         * Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod in the same node, zone, etc. as some other pod(s)).
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPatch {
            /**
             * The scheduler will prefer to schedule pods to nodes that satisfy
             * the anti-affinity expressions specified by this field, but it may choose
             * a node that violates one or more of the expressions. The node that is
             * most preferred is the one with the greatest sum of weights, i.e.
             * for each node that meets all of the scheduling requirements (resource
             * request, requiredDuringScheduling anti-affinity expressions, etc.),
             * compute a sum by iterating through the elements of this field and adding
             * "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
             * node(s) with the highest sum are the most preferred.
             */
            preferredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
            /**
             * If the anti-affinity requirements specified by this field are not met at
             * scheduling time, the pod will not be scheduled onto the node.
             * If the anti-affinity requirements specified by this field cease to be met
             * at some point during pod execution (e.g. due to a pod label update), the
             * system may or may not try to eventually evict the pod from its node.
             * When there are multiple elements, the lists of nodes corresponding to each
             * podAffinityTerm are intersected, i.e. all terms must be satisfied.
             */
            requiredDuringSchedulingIgnoredDuringExecution?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch>[]>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecution {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPatch {
            podAffinityTerm?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch>;
            /**
             * weight associated with matching the corresponding podAffinityTerm,
             * in the range 1-100.
             */
            weight?: pulumi.Input<number>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTerm {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Required. A pod affinity term, associated with the corresponding weight.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityPreferredDuringSchedulingIgnoredDuringExecutionPodAffinityTermNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecution {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over a set of resources, in this case pods.
         * If it's null, this PodAffinityTerm matches with no Pods.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelector {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressions {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label selector requirement is a selector that contains values, a key, and an operator that
         * relates the key and values.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch {
            /**
             * key is the label key that the selector applies to.
             */
            key?: pulumi.Input<string>;
            /**
             * operator represents a key's relationship to a set of values.
             * Valid operators are In, NotIn, Exists and DoesNotExist.
             */
            operator?: pulumi.Input<string>;
            /**
             * values is an array of string values. If the operator is In or NotIn,
             * the values array must be non-empty. If the operator is Exists or DoesNotExist,
             * the values array must be empty. This array is replaced during a strategic
             * merge patch.
             */
            values?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * A label query over the set of namespaces that the term applies to.
         * The term is applied to the union of the namespaces selected by this field
         * and the ones listed in the namespaces field.
         * null selector and null or empty namespaces list means "this pod's namespace".
         * An empty selector ({}) matches all namespaces.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch {
            /**
             * matchExpressions is a list of label selector requirements. The requirements are ANDed.
             */
            matchExpressions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorMatchExpressionsPatch>[]>;
            /**
             * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
             * map is equivalent to an element of matchExpressions, whose key field is "key", the
             * operator is "In", and the values array contains only "value". The requirements are ANDed.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Defines a set of pods (namely those matching the labelSelector
         * relative to the given namespace(s)) that this pod should be
         * co-located (affinity) or not co-located (anti-affinity) with,
         * where co-located is defined as running on a node whose value of
         * the label with key <topologyKey> matches that of any node on which
         * a pod of the set of pods is running
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionPatch {
            labelSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionLabelSelectorPatch>;
            /**
             * MatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both matchLabelKeys and labelSelector.
             * Also, matchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            matchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * MismatchLabelKeys is a set of pod label keys to select which pods will
             * be taken into consideration. The keys are used to lookup values from the
             * incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
             * to select the group of existing pods which pods will be taken into consideration
             * for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
             * pod labels will be ignored. The default value is empty.
             * The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
             * Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
             * This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
             */
            mismatchLabelKeys?: pulumi.Input<pulumi.Input<string>[]>;
            namespaceSelector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPodAntiAffinityRequiredDuringSchedulingIgnoredDuringExecutionNamespaceSelectorPatch>;
            /**
             * namespaces specifies a static list of namespace names that the term applies to.
             * The term is applied to the union of the namespaces listed in this field
             * and the ones selected by namespaceSelector.
             * null or empty namespaces list and null namespaceSelector means "this pod's namespace".
             */
            namespaces?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
             * the labelSelector in the specified namespaces, where co-located is defined as running on a node
             * whose value of the label with key topologyKey matches that of any node on which any of the
             * selected pods is running.
             * Empty topologyKey is not allowed.
             */
            topologyKey?: pulumi.Input<string>;
        }

        /**
         * LocalObjectReference contains enough information to let you locate the
         * referenced object inside the same namespace.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecImagePullSecrets {
            /**
             * Name of the referent.
             * This field is effectively required, but due to backwards compatibility is
             * allowed to be empty. Instances of this type with an empty value here are
             * almost certainly wrong.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * LocalObjectReference contains enough information to let you locate the
         * referenced object inside the same namespace.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecImagePullSecretsPatch {
            /**
             * Name of the referent.
             * This field is effectively required, but due to backwards compatibility is
             * allowed to be empty. Instances of this type with an empty value here are
             * almost certainly wrong.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * PodSpec defines overrides for the HTTP01 challenge solver pod.
         * Check ACMEChallengeSolverHTTP01IngressPodSpec to find out currently supported fields.
         * All other fields will be ignored.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecPatch {
            affinity?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecAffinityPatch>;
            /**
             * If specified, the pod's imagePullSecrets
             */
            imagePullSecrets?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecImagePullSecretsPatch>[]>;
            /**
             * NodeSelector is a selector which must be true for the pod to fit on a node.
             * Selector which must match a node's labels for the pod to be scheduled on that node.
             * More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
             */
            nodeSelector?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * If specified, the pod's priorityClassName.
             */
            priorityClassName?: pulumi.Input<string>;
            securityContext?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextPatch>;
            /**
             * If specified, the pod's service account
             */
            serviceAccountName?: pulumi.Input<string>;
            /**
             * If specified, the pod's tolerations.
             */
            tolerations?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecTolerationsPatch>[]>;
        }

        /**
         * If specified, the pod's security context
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContext {
            /**
             * A special supplemental group that applies to all containers in a pod.
             * Some volume types allow the Kubelet to change the ownership of that volume
             * to be owned by the pod:
             *
             * 1. The owning GID will be the FSGroup
             * 2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
             * 3. The permission bits are OR'd with rw-rw----
             *
             * If unset, the Kubelet will not modify the ownership and permissions of any volume.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroup?: pulumi.Input<number>;
            /**
             * fsGroupChangePolicy defines behavior of changing ownership and permission of the volume
             * before being exposed inside Pod. This field will only apply to
             * volume types which support fsGroup based ownership(and permissions).
             * It will have no effect on ephemeral volume types such as: secret, configmaps
             * and emptydir.
             * Valid values are "OnRootMismatch" and "Always". If not specified, "Always" is used.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroupChangePolicy?: pulumi.Input<string>;
            /**
             * The GID to run the entrypoint of the container process.
             * Uses runtime default if unset.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsGroup?: pulumi.Input<number>;
            /**
             * Indicates that the container must run as a non-root user.
             * If true, the Kubelet will validate the image at runtime to ensure that it
             * does not run as UID 0 (root) and fail to start the container if it does.
             * If unset or false, no such validation will be performed.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence.
             */
            runAsNonRoot?: pulumi.Input<boolean>;
            /**
             * The UID to run the entrypoint of the container process.
             * Defaults to user specified in image metadata if unspecified.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsUser?: pulumi.Input<number>;
            seLinuxOptions?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeLinuxOptions>;
            seccompProfile?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeccompProfile>;
            /**
             * A list of groups applied to the first process run in each container, in addition
             * to the container's primary GID, the fsGroup (if specified), and group memberships
             * defined in the container image for the uid of the container process. If unspecified,
             * no additional groups are added to any container. Note that group memberships
             * defined in the container image for the uid of the container process are still effective,
             * even if they are not included in this list.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            supplementalGroups?: pulumi.Input<pulumi.Input<number>[]>;
            /**
             * Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
             * sysctls (by the container runtime) might fail to launch.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            sysctls?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSysctls>[]>;
        }

        /**
         * If specified, the pod's security context
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextPatch {
            /**
             * A special supplemental group that applies to all containers in a pod.
             * Some volume types allow the Kubelet to change the ownership of that volume
             * to be owned by the pod:
             *
             * 1. The owning GID will be the FSGroup
             * 2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
             * 3. The permission bits are OR'd with rw-rw----
             *
             * If unset, the Kubelet will not modify the ownership and permissions of any volume.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroup?: pulumi.Input<number>;
            /**
             * fsGroupChangePolicy defines behavior of changing ownership and permission of the volume
             * before being exposed inside Pod. This field will only apply to
             * volume types which support fsGroup based ownership(and permissions).
             * It will have no effect on ephemeral volume types such as: secret, configmaps
             * and emptydir.
             * Valid values are "OnRootMismatch" and "Always". If not specified, "Always" is used.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            fsGroupChangePolicy?: pulumi.Input<string>;
            /**
             * The GID to run the entrypoint of the container process.
             * Uses runtime default if unset.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsGroup?: pulumi.Input<number>;
            /**
             * Indicates that the container must run as a non-root user.
             * If true, the Kubelet will validate the image at runtime to ensure that it
             * does not run as UID 0 (root) and fail to start the container if it does.
             * If unset or false, no such validation will be performed.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence.
             */
            runAsNonRoot?: pulumi.Input<boolean>;
            /**
             * The UID to run the entrypoint of the container process.
             * Defaults to user specified in image metadata if unspecified.
             * May also be set in SecurityContext.  If set in both SecurityContext and
             * PodSecurityContext, the value specified in SecurityContext takes precedence
             * for that container.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            runAsUser?: pulumi.Input<number>;
            seLinuxOptions?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeLinuxOptionsPatch>;
            seccompProfile?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeccompProfilePatch>;
            /**
             * A list of groups applied to the first process run in each container, in addition
             * to the container's primary GID, the fsGroup (if specified), and group memberships
             * defined in the container image for the uid of the container process. If unspecified,
             * no additional groups are added to any container. Note that group memberships
             * defined in the container image for the uid of the container process are still effective,
             * even if they are not included in this list.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            supplementalGroups?: pulumi.Input<pulumi.Input<number>[]>;
            /**
             * Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
             * sysctls (by the container runtime) might fail to launch.
             * Note that this field cannot be set when spec.os.name is windows.
             */
            sysctls?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSysctlsPatch>[]>;
        }

        /**
         * The SELinux context to be applied to all containers.
         * If unspecified, the container runtime will allocate a random SELinux context for each
         * container.  May also be set in SecurityContext.  If set in
         * both SecurityContext and PodSecurityContext, the value specified in SecurityContext
         * takes precedence for that container.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeLinuxOptions {
            /**
             * Level is SELinux level label that applies to the container.
             */
            level?: pulumi.Input<string>;
            /**
             * Role is a SELinux role label that applies to the container.
             */
            role?: pulumi.Input<string>;
            /**
             * Type is a SELinux type label that applies to the container.
             */
            type?: pulumi.Input<string>;
            /**
             * User is a SELinux user label that applies to the container.
             */
            user?: pulumi.Input<string>;
        }

        /**
         * The SELinux context to be applied to all containers.
         * If unspecified, the container runtime will allocate a random SELinux context for each
         * container.  May also be set in SecurityContext.  If set in
         * both SecurityContext and PodSecurityContext, the value specified in SecurityContext
         * takes precedence for that container.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeLinuxOptionsPatch {
            /**
             * Level is SELinux level label that applies to the container.
             */
            level?: pulumi.Input<string>;
            /**
             * Role is a SELinux role label that applies to the container.
             */
            role?: pulumi.Input<string>;
            /**
             * Type is a SELinux type label that applies to the container.
             */
            type?: pulumi.Input<string>;
            /**
             * User is a SELinux user label that applies to the container.
             */
            user?: pulumi.Input<string>;
        }

        /**
         * The seccomp options to use by the containers in this pod.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeccompProfile {
            /**
             * localhostProfile indicates a profile defined in a file on the node should be used.
             * The profile must be preconfigured on the node to work.
             * Must be a descending path, relative to the kubelet's configured seccomp profile location.
             * Must be set if type is "Localhost". Must NOT be set for any other type.
             */
            localhostProfile?: pulumi.Input<string>;
            /**
             * type indicates which kind of seccomp profile will be applied.
             * Valid options are:
             *
             * Localhost - a profile defined in a file on the node should be used.
             * RuntimeDefault - the container runtime default profile should be used.
             * Unconfined - no profile should be applied.
             */
            type?: pulumi.Input<string>;
        }

        /**
         * The seccomp options to use by the containers in this pod.
         * Note that this field cannot be set when spec.os.name is windows.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSeccompProfilePatch {
            /**
             * localhostProfile indicates a profile defined in a file on the node should be used.
             * The profile must be preconfigured on the node to work.
             * Must be a descending path, relative to the kubelet's configured seccomp profile location.
             * Must be set if type is "Localhost". Must NOT be set for any other type.
             */
            localhostProfile?: pulumi.Input<string>;
            /**
             * type indicates which kind of seccomp profile will be applied.
             * Valid options are:
             *
             * Localhost - a profile defined in a file on the node should be used.
             * RuntimeDefault - the container runtime default profile should be used.
             * Unconfined - no profile should be applied.
             */
            type?: pulumi.Input<string>;
        }

        /**
         * Sysctl defines a kernel parameter to be set
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSysctls {
            /**
             * Name of a property to set
             */
            name?: pulumi.Input<string>;
            /**
             * Value of a property to set
             */
            value?: pulumi.Input<string>;
        }

        /**
         * Sysctl defines a kernel parameter to be set
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecSecurityContextSysctlsPatch {
            /**
             * Name of a property to set
             */
            name?: pulumi.Input<string>;
            /**
             * Value of a property to set
             */
            value?: pulumi.Input<string>;
        }

        /**
         * The pod this Toleration is attached to tolerates any taint that matches
         * the triple <key,value,effect> using the matching operator <operator>.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecTolerations {
            /**
             * Effect indicates the taint effect to match. Empty means match all taint effects.
             * When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
             */
            effect?: pulumi.Input<string>;
            /**
             * Key is the taint key that the toleration applies to. Empty means match all taint keys.
             * If the key is empty, operator must be Exists; this combination means to match all values and all keys.
             */
            key?: pulumi.Input<string>;
            /**
             * Operator represents a key's relationship to the value.
             * Valid operators are Exists and Equal. Defaults to Equal.
             * Exists is equivalent to wildcard for value, so that a pod can
             * tolerate all taints of a particular category.
             */
            operator?: pulumi.Input<string>;
            /**
             * TolerationSeconds represents the period of time the toleration (which must be
             * of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
             * it is not set, which means tolerate the taint forever (do not evict). Zero and
             * negative values will be treated as 0 (evict immediately) by the system.
             */
            tolerationSeconds?: pulumi.Input<number>;
            /**
             * Value is the taint value the toleration matches to.
             * If the operator is Exists, the value should be empty, otherwise just a regular string.
             */
            value?: pulumi.Input<string>;
        }

        /**
         * The pod this Toleration is attached to tolerates any taint that matches
         * the triple <key,value,effect> using the matching operator <operator>.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01IngressPodTemplateSpecTolerationsPatch {
            /**
             * Effect indicates the taint effect to match. Empty means match all taint effects.
             * When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
             */
            effect?: pulumi.Input<string>;
            /**
             * Key is the taint key that the toleration applies to. Empty means match all taint keys.
             * If the key is empty, operator must be Exists; this combination means to match all values and all keys.
             */
            key?: pulumi.Input<string>;
            /**
             * Operator represents a key's relationship to the value.
             * Valid operators are Exists and Equal. Defaults to Equal.
             * Exists is equivalent to wildcard for value, so that a pod can
             * tolerate all taints of a particular category.
             */
            operator?: pulumi.Input<string>;
            /**
             * TolerationSeconds represents the period of time the toleration (which must be
             * of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
             * it is not set, which means tolerate the taint forever (do not evict). Zero and
             * negative values will be treated as 0 (evict immediately) by the system.
             */
            tolerationSeconds?: pulumi.Input<number>;
            /**
             * Value is the taint value the toleration matches to.
             * If the operator is Exists, the value should be empty, otherwise just a regular string.
             */
            value?: pulumi.Input<string>;
        }

        /**
         * Configures cert-manager to attempt to complete authorizations by
         * performing the HTTP01 challenge flow.
         * It is not possible to obtain certificates for wildcard domain names
         * (e.g. `*.example.com`) using the HTTP01 challenge mechanism.
         */
        export interface ClusterIssuerSpecAcmeSolversHttp01Patch {
            gatewayHTTPRoute?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01GatewayHTTPRoutePatch>;
            ingress?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01IngressPatch>;
        }

        /**
         * An ACMEChallengeSolver describes how to solve ACME challenges for the issuer it is part of.
         * A selector may be provided to use different solving strategies for different DNS names.
         * Only one of HTTP01 or DNS01 must be provided.
         */
        export interface ClusterIssuerSpecAcmeSolversPatch {
            dns01?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversDns01Patch>;
            http01?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversHttp01Patch>;
            selector?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmeSolversSelectorPatch>;
        }

        /**
         * Selector selects a set of DNSNames on the Certificate resource that
         * should be solved using this challenge solver.
         * If not specified, the solver will be treated as the 'default' solver
         * with the lowest priority, i.e. if any other solver has a more specific
         * match, it will be used instead.
         */
        export interface ClusterIssuerSpecAcmeSolversSelector {
            /**
             * List of DNSNames that this solver will be used to solve.
             * If specified and a match is found, a dnsNames selector will take
             * precedence over a dnsZones selector.
             * If multiple solvers match with the same dnsNames value, the solver
             * with the most matching labels in matchLabels will be selected.
             * If neither has more matches, the solver defined earlier in the list
             * will be selected.
             */
            dnsNames?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * List of DNSZones that this solver will be used to solve.
             * The most specific DNS zone match specified here will take precedence
             * over other DNS zone matches, so a solver specifying sys.example.com
             * will be selected over one specifying example.com for the domain
             * www.sys.example.com.
             * If multiple solvers match with the same dnsZones value, the solver
             * with the most matching labels in matchLabels will be selected.
             * If neither has more matches, the solver defined earlier in the list
             * will be selected.
             */
            dnsZones?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * A label selector that is used to refine the set of certificate's that
             * this challenge solver will apply to.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * Selector selects a set of DNSNames on the Certificate resource that
         * should be solved using this challenge solver.
         * If not specified, the solver will be treated as the 'default' solver
         * with the lowest priority, i.e. if any other solver has a more specific
         * match, it will be used instead.
         */
        export interface ClusterIssuerSpecAcmeSolversSelectorPatch {
            /**
             * List of DNSNames that this solver will be used to solve.
             * If specified and a match is found, a dnsNames selector will take
             * precedence over a dnsZones selector.
             * If multiple solvers match with the same dnsNames value, the solver
             * with the most matching labels in matchLabels will be selected.
             * If neither has more matches, the solver defined earlier in the list
             * will be selected.
             */
            dnsNames?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * List of DNSZones that this solver will be used to solve.
             * The most specific DNS zone match specified here will take precedence
             * over other DNS zone matches, so a solver specifying sys.example.com
             * will be selected over one specifying example.com for the domain
             * www.sys.example.com.
             * If multiple solvers match with the same dnsZones value, the solver
             * with the most matching labels in matchLabels will be selected.
             * If neither has more matches, the solver defined earlier in the list
             * will be selected.
             */
            dnsZones?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * A label selector that is used to refine the set of certificate's that
             * this challenge solver will apply to.
             */
            matchLabels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
        }

        /**
         * CA configures this issuer to sign certificates using a signing CA keypair
         * stored in a Secret resource.
         * This is used to build internal PKIs that are managed by cert-manager.
         */
        export interface ClusterIssuerSpecCa {
            /**
             * The CRL distribution points is an X.509 v3 certificate extension which identifies
             * the location of the CRL from which the revocation of this certificate can be checked.
             * If not set, certificates will be issued without distribution points set.
             */
            crlDistributionPoints?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * IssuingCertificateURLs is a list of URLs which this issuer should embed into certificates
             * it creates. See https://www.rfc-editor.org/rfc/rfc5280#section-4.2.2.1 for more details.
             * As an example, such a URL might be "http://ca.domain.com/ca.crt".
             */
            issuingCertificateURLs?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * The OCSP server list is an X.509 v3 extension that defines a list of
             * URLs of OCSP responders. The OCSP responders can be queried for the
             * revocation status of an issued certificate. If not set, the
             * certificate will be issued with no OCSP servers set. For example, an
             * OCSP server URL could be "http://ocsp.int-x3.letsencrypt.org".
             */
            ocspServers?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * SecretName is the name of the secret used to sign Certificates issued
             * by this Issuer.
             */
            secretName?: pulumi.Input<string>;
        }

        /**
         * CA configures this issuer to sign certificates using a signing CA keypair
         * stored in a Secret resource.
         * This is used to build internal PKIs that are managed by cert-manager.
         */
        export interface ClusterIssuerSpecCaPatch {
            /**
             * The CRL distribution points is an X.509 v3 certificate extension which identifies
             * the location of the CRL from which the revocation of this certificate can be checked.
             * If not set, certificates will be issued without distribution points set.
             */
            crlDistributionPoints?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * IssuingCertificateURLs is a list of URLs which this issuer should embed into certificates
             * it creates. See https://www.rfc-editor.org/rfc/rfc5280#section-4.2.2.1 for more details.
             * As an example, such a URL might be "http://ca.domain.com/ca.crt".
             */
            issuingCertificateURLs?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * The OCSP server list is an X.509 v3 extension that defines a list of
             * URLs of OCSP responders. The OCSP responders can be queried for the
             * revocation status of an issued certificate. If not set, the
             * certificate will be issued with no OCSP servers set. For example, an
             * OCSP server URL could be "http://ocsp.int-x3.letsencrypt.org".
             */
            ocspServers?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * SecretName is the name of the secret used to sign Certificates issued
             * by this Issuer.
             */
            secretName?: pulumi.Input<string>;
        }

        /**
         * Desired state of the ClusterIssuer resource.
         */
        export interface ClusterIssuerSpecPatch {
            acme?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecAcmePatch>;
            ca?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecCaPatch>;
            selfSigned?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecSelfSignedPatch>;
            vault?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultPatch>;
            venafi?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiPatch>;
        }

        /**
         * SelfSigned configures this issuer to 'self sign' certificates using the
         * private key used to create the CertificateRequest object.
         */
        export interface ClusterIssuerSpecSelfSigned {
            /**
             * The CRL distribution points is an X.509 v3 certificate extension which identifies
             * the location of the CRL from which the revocation of this certificate can be checked.
             * If not set certificate will be issued without CDP. Values are strings.
             */
            crlDistributionPoints?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * SelfSigned configures this issuer to 'self sign' certificates using the
         * private key used to create the CertificateRequest object.
         */
        export interface ClusterIssuerSpecSelfSignedPatch {
            /**
             * The CRL distribution points is an X.509 v3 certificate extension which identifies
             * the location of the CRL from which the revocation of this certificate can be checked.
             * If not set certificate will be issued without CDP. Values are strings.
             */
            crlDistributionPoints?: pulumi.Input<pulumi.Input<string>[]>;
        }

        /**
         * Vault configures this issuer to sign certificates using a HashiCorp Vault
         * PKI backend.
         */
        export interface ClusterIssuerSpecVault {
            auth?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuth>;
            /**
             * Base64-encoded bundle of PEM CAs which will be used to validate the certificate
             * chain presented by Vault. Only used if using HTTPS to connect to Vault and
             * ignored for HTTP connections.
             * Mutually exclusive with CABundleSecretRef.
             * If neither CABundle nor CABundleSecretRef are defined, the certificate bundle in
             * the cert-manager controller container is used to validate the TLS connection.
             */
            caBundle?: pulumi.Input<string>;
            caBundleSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultCaBundleSecretRef>;
            clientCertSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultClientCertSecretRef>;
            clientKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultClientKeySecretRef>;
            /**
             * Name of the vault namespace. Namespaces is a set of features within Vault Enterprise that allows Vault environments to support Secure Multi-tenancy. e.g: "ns1"
             * More about namespaces can be found here https://www.vaultproject.io/docs/enterprise/namespaces
             */
            namespace?: pulumi.Input<string>;
            /**
             * Path is the mount path of the Vault PKI backend's `sign` endpoint, e.g:
             * "my_pki_mount/sign/my-role-name".
             */
            path?: pulumi.Input<string>;
            /**
             * Server is the connection address for the Vault server, e.g: "https://vault.example.com:8200".
             */
            server?: pulumi.Input<string>;
        }

        /**
         * Auth configures how cert-manager authenticates with the Vault server.
         */
        export interface ClusterIssuerSpecVaultAuth {
            appRole?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthAppRole>;
            clientCertificate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthClientCertificate>;
            kubernetes?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthKubernetes>;
            tokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthTokenSecretRef>;
        }

        /**
         * AppRole authenticates with Vault using the App Role auth mechanism,
         * with the role and secret stored in a Kubernetes Secret resource.
         */
        export interface ClusterIssuerSpecVaultAuthAppRole {
            /**
             * Path where the App Role authentication backend is mounted in Vault, e.g:
             * "approle"
             */
            path?: pulumi.Input<string>;
            /**
             * RoleID configured in the App Role authentication backend when setting
             * up the authentication backend in Vault.
             */
            roleId?: pulumi.Input<string>;
            secretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthAppRoleSecretRef>;
        }

        /**
         * AppRole authenticates with Vault using the App Role auth mechanism,
         * with the role and secret stored in a Kubernetes Secret resource.
         */
        export interface ClusterIssuerSpecVaultAuthAppRolePatch {
            /**
             * Path where the App Role authentication backend is mounted in Vault, e.g:
             * "approle"
             */
            path?: pulumi.Input<string>;
            /**
             * RoleID configured in the App Role authentication backend when setting
             * up the authentication backend in Vault.
             */
            roleId?: pulumi.Input<string>;
            secretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthAppRoleSecretRefPatch>;
        }

        /**
         * Reference to a key in a Secret that contains the App Role secret used
         * to authenticate with Vault.
         * The `key` field must be specified and denotes which entry within the Secret
         * resource is used as the app role secret.
         */
        export interface ClusterIssuerSpecVaultAuthAppRoleSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a key in a Secret that contains the App Role secret used
         * to authenticate with Vault.
         * The `key` field must be specified and denotes which entry within the Secret
         * resource is used as the app role secret.
         */
        export interface ClusterIssuerSpecVaultAuthAppRoleSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * ClientCertificate authenticates with Vault by presenting a client
         * certificate during the request's TLS handshake.
         * Works only when using HTTPS protocol.
         */
        export interface ClusterIssuerSpecVaultAuthClientCertificate {
            /**
             * The Vault mountPath here is the mount path to use when authenticating with
             * Vault. For example, setting a value to `/v1/auth/foo`, will use the path
             * `/v1/auth/foo/login` to authenticate with Vault. If unspecified, the
             * default value "/v1/auth/cert" will be used.
             */
            mountPath?: pulumi.Input<string>;
            /**
             * Name of the certificate role to authenticate against.
             * If not set, matching any certificate role, if available.
             */
            name?: pulumi.Input<string>;
            /**
             * Reference to Kubernetes Secret of type "kubernetes.io/tls" (hence containing
             * tls.crt and tls.key) used to authenticate to Vault using TLS client
             * authentication.
             */
            secretName?: pulumi.Input<string>;
        }

        /**
         * ClientCertificate authenticates with Vault by presenting a client
         * certificate during the request's TLS handshake.
         * Works only when using HTTPS protocol.
         */
        export interface ClusterIssuerSpecVaultAuthClientCertificatePatch {
            /**
             * The Vault mountPath here is the mount path to use when authenticating with
             * Vault. For example, setting a value to `/v1/auth/foo`, will use the path
             * `/v1/auth/foo/login` to authenticate with Vault. If unspecified, the
             * default value "/v1/auth/cert" will be used.
             */
            mountPath?: pulumi.Input<string>;
            /**
             * Name of the certificate role to authenticate against.
             * If not set, matching any certificate role, if available.
             */
            name?: pulumi.Input<string>;
            /**
             * Reference to Kubernetes Secret of type "kubernetes.io/tls" (hence containing
             * tls.crt and tls.key) used to authenticate to Vault using TLS client
             * authentication.
             */
            secretName?: pulumi.Input<string>;
        }

        /**
         * Kubernetes authenticates with Vault by passing the ServiceAccount
         * token stored in the named Secret resource to the Vault server.
         */
        export interface ClusterIssuerSpecVaultAuthKubernetes {
            /**
             * The Vault mountPath here is the mount path to use when authenticating with
             * Vault. For example, setting a value to `/v1/auth/foo`, will use the path
             * `/v1/auth/foo/login` to authenticate with Vault. If unspecified, the
             * default value "/v1/auth/kubernetes" will be used.
             */
            mountPath?: pulumi.Input<string>;
            /**
             * A required field containing the Vault Role to assume. A Role binds a
             * Kubernetes ServiceAccount with a set of Vault policies.
             */
            role?: pulumi.Input<string>;
            secretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthKubernetesSecretRef>;
            serviceAccountRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthKubernetesServiceAccountRef>;
        }

        /**
         * Kubernetes authenticates with Vault by passing the ServiceAccount
         * token stored in the named Secret resource to the Vault server.
         */
        export interface ClusterIssuerSpecVaultAuthKubernetesPatch {
            /**
             * The Vault mountPath here is the mount path to use when authenticating with
             * Vault. For example, setting a value to `/v1/auth/foo`, will use the path
             * `/v1/auth/foo/login` to authenticate with Vault. If unspecified, the
             * default value "/v1/auth/kubernetes" will be used.
             */
            mountPath?: pulumi.Input<string>;
            /**
             * A required field containing the Vault Role to assume. A Role binds a
             * Kubernetes ServiceAccount with a set of Vault policies.
             */
            role?: pulumi.Input<string>;
            secretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthKubernetesSecretRefPatch>;
            serviceAccountRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthKubernetesServiceAccountRefPatch>;
        }

        /**
         * The required Secret field containing a Kubernetes ServiceAccount JWT used
         * for authenticating with Vault. Use of 'ambient credentials' is not
         * supported.
         */
        export interface ClusterIssuerSpecVaultAuthKubernetesSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * The required Secret field containing a Kubernetes ServiceAccount JWT used
         * for authenticating with Vault. Use of 'ambient credentials' is not
         * supported.
         */
        export interface ClusterIssuerSpecVaultAuthKubernetesSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a service account that will be used to request a bound
         * token (also known as "projected token"). Compared to using "secretRef",
         * using this field means that you don't rely on statically bound tokens. To
         * use this field, you must configure an RBAC rule to let cert-manager
         * request a token.
         */
        export interface ClusterIssuerSpecVaultAuthKubernetesServiceAccountRef {
            /**
             * TokenAudiences is an optional list of extra audiences to include in the token passed to Vault. The default token
             * consisting of the issuer's namespace and name is always included.
             */
            audiences?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Name of the ServiceAccount used to request a token.
             */
            name?: pulumi.Input<string>;
        }

        /**
         * A reference to a service account that will be used to request a bound
         * token (also known as "projected token"). Compared to using "secretRef",
         * using this field means that you don't rely on statically bound tokens. To
         * use this field, you must configure an RBAC rule to let cert-manager
         * request a token.
         */
        export interface ClusterIssuerSpecVaultAuthKubernetesServiceAccountRefPatch {
            /**
             * TokenAudiences is an optional list of extra audiences to include in the token passed to Vault. The default token
             * consisting of the issuer's namespace and name is always included.
             */
            audiences?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * Name of the ServiceAccount used to request a token.
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Auth configures how cert-manager authenticates with the Vault server.
         */
        export interface ClusterIssuerSpecVaultAuthPatch {
            appRole?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthAppRolePatch>;
            clientCertificate?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthClientCertificatePatch>;
            kubernetes?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthKubernetesPatch>;
            tokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthTokenSecretRefPatch>;
        }

        /**
         * TokenSecretRef authenticates with Vault by presenting a token.
         */
        export interface ClusterIssuerSpecVaultAuthTokenSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * TokenSecretRef authenticates with Vault by presenting a token.
         */
        export interface ClusterIssuerSpecVaultAuthTokenSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a bundle of PEM-encoded CAs to use when
         * verifying the certificate chain presented by Vault when using HTTPS.
         * Mutually exclusive with CABundle.
         * If neither CABundle nor CABundleSecretRef are defined, the certificate bundle in
         * the cert-manager controller container is used to validate the TLS connection.
         * If no key for the Secret is specified, cert-manager will default to 'ca.crt'.
         */
        export interface ClusterIssuerSpecVaultCaBundleSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a bundle of PEM-encoded CAs to use when
         * verifying the certificate chain presented by Vault when using HTTPS.
         * Mutually exclusive with CABundle.
         * If neither CABundle nor CABundleSecretRef are defined, the certificate bundle in
         * the cert-manager controller container is used to validate the TLS connection.
         * If no key for the Secret is specified, cert-manager will default to 'ca.crt'.
         */
        export interface ClusterIssuerSpecVaultCaBundleSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a PEM-encoded Client Certificate to use when the
         * Vault server requires mTLS.
         */
        export interface ClusterIssuerSpecVaultClientCertSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a PEM-encoded Client Certificate to use when the
         * Vault server requires mTLS.
         */
        export interface ClusterIssuerSpecVaultClientCertSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a PEM-encoded Client Private Key to use when the
         * Vault server requires mTLS.
         */
        export interface ClusterIssuerSpecVaultClientKeySecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a PEM-encoded Client Private Key to use when the
         * Vault server requires mTLS.
         */
        export interface ClusterIssuerSpecVaultClientKeySecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Vault configures this issuer to sign certificates using a HashiCorp Vault
         * PKI backend.
         */
        export interface ClusterIssuerSpecVaultPatch {
            auth?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultAuthPatch>;
            /**
             * Base64-encoded bundle of PEM CAs which will be used to validate the certificate
             * chain presented by Vault. Only used if using HTTPS to connect to Vault and
             * ignored for HTTP connections.
             * Mutually exclusive with CABundleSecretRef.
             * If neither CABundle nor CABundleSecretRef are defined, the certificate bundle in
             * the cert-manager controller container is used to validate the TLS connection.
             */
            caBundle?: pulumi.Input<string>;
            caBundleSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultCaBundleSecretRefPatch>;
            clientCertSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultClientCertSecretRefPatch>;
            clientKeySecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVaultClientKeySecretRefPatch>;
            /**
             * Name of the vault namespace. Namespaces is a set of features within Vault Enterprise that allows Vault environments to support Secure Multi-tenancy. e.g: "ns1"
             * More about namespaces can be found here https://www.vaultproject.io/docs/enterprise/namespaces
             */
            namespace?: pulumi.Input<string>;
            /**
             * Path is the mount path of the Vault PKI backend's `sign` endpoint, e.g:
             * "my_pki_mount/sign/my-role-name".
             */
            path?: pulumi.Input<string>;
            /**
             * Server is the connection address for the Vault server, e.g: "https://vault.example.com:8200".
             */
            server?: pulumi.Input<string>;
        }

        /**
         * Venafi configures this issuer to sign certificates using a Venafi TPP
         * or Venafi Cloud policy zone.
         */
        export interface ClusterIssuerSpecVenafi {
            cloud?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiCloud>;
            tpp?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiTpp>;
            /**
             * Zone is the Venafi Policy Zone to use for this issuer.
             * All requests made to the Venafi platform will be restricted by the named
             * zone policy.
             * This field is required.
             */
            zone?: pulumi.Input<string>;
        }

        /**
         * Cloud specifies the Venafi cloud configuration settings.
         * Only one of TPP or Cloud may be specified.
         */
        export interface ClusterIssuerSpecVenafiCloud {
            apiTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiCloudApiTokenSecretRef>;
            /**
             * URL is the base URL for Venafi Cloud.
             * Defaults to "https://api.venafi.cloud/v1".
             */
            url?: pulumi.Input<string>;
        }

        /**
         * APITokenSecretRef is a secret key selector for the Venafi Cloud API token.
         */
        export interface ClusterIssuerSpecVenafiCloudApiTokenSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * APITokenSecretRef is a secret key selector for the Venafi Cloud API token.
         */
        export interface ClusterIssuerSpecVenafiCloudApiTokenSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Cloud specifies the Venafi cloud configuration settings.
         * Only one of TPP or Cloud may be specified.
         */
        export interface ClusterIssuerSpecVenafiCloudPatch {
            apiTokenSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiCloudApiTokenSecretRefPatch>;
            /**
             * URL is the base URL for Venafi Cloud.
             * Defaults to "https://api.venafi.cloud/v1".
             */
            url?: pulumi.Input<string>;
        }

        /**
         * Venafi configures this issuer to sign certificates using a Venafi TPP
         * or Venafi Cloud policy zone.
         */
        export interface ClusterIssuerSpecVenafiPatch {
            cloud?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiCloudPatch>;
            tpp?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiTppPatch>;
            /**
             * Zone is the Venafi Policy Zone to use for this issuer.
             * All requests made to the Venafi platform will be restricted by the named
             * zone policy.
             * This field is required.
             */
            zone?: pulumi.Input<string>;
        }

        /**
         * TPP specifies Trust Protection Platform configuration settings.
         * Only one of TPP or Cloud may be specified.
         */
        export interface ClusterIssuerSpecVenafiTpp {
            /**
             * Base64-encoded bundle of PEM CAs which will be used to validate the certificate
             * chain presented by the TPP server. Only used if using HTTPS; ignored for HTTP.
             * If undefined, the certificate bundle in the cert-manager controller container
             * is used to validate the chain.
             */
            caBundle?: pulumi.Input<string>;
            caBundleSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiTppCaBundleSecretRef>;
            credentialsRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiTppCredentialsRef>;
            /**
             * URL is the base URL for the vedsdk endpoint of the Venafi TPP instance,
             * for example: "https://tpp.example.com/vedsdk".
             */
            url?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a base64-encoded bundle of PEM CAs
         * which will be used to validate the certificate chain presented by the TPP server.
         * Only used if using HTTPS; ignored for HTTP. Mutually exclusive with CABundle.
         * If neither CABundle nor CABundleSecretRef is defined, the certificate bundle in
         * the cert-manager controller container is used to validate the TLS connection.
         */
        export interface ClusterIssuerSpecVenafiTppCaBundleSecretRef {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * Reference to a Secret containing a base64-encoded bundle of PEM CAs
         * which will be used to validate the certificate chain presented by the TPP server.
         * Only used if using HTTPS; ignored for HTTP. Mutually exclusive with CABundle.
         * If neither CABundle nor CABundleSecretRef is defined, the certificate bundle in
         * the cert-manager controller container is used to validate the TLS connection.
         */
        export interface ClusterIssuerSpecVenafiTppCaBundleSecretRefPatch {
            /**
             * The key of the entry in the Secret resource's `data` field to be used.
             * Some instances of this field may be defaulted, in others it may be
             * required.
             */
            key?: pulumi.Input<string>;
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * CredentialsRef is a reference to a Secret containing the Venafi TPP API credentials.
         * The secret must contain the key 'access-token' for the Access Token Authentication,
         * or two keys, 'username' and 'password' for the API Keys Authentication.
         */
        export interface ClusterIssuerSpecVenafiTppCredentialsRef {
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * CredentialsRef is a reference to a Secret containing the Venafi TPP API credentials.
         * The secret must contain the key 'access-token' for the Access Token Authentication,
         * or two keys, 'username' and 'password' for the API Keys Authentication.
         */
        export interface ClusterIssuerSpecVenafiTppCredentialsRefPatch {
            /**
             * Name of the resource being referred to.
             * More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
             */
            name?: pulumi.Input<string>;
        }

        /**
         * TPP specifies Trust Protection Platform configuration settings.
         * Only one of TPP or Cloud may be specified.
         */
        export interface ClusterIssuerSpecVenafiTppPatch {
            /**
             * Base64-encoded bundle of PEM CAs which will be used to validate the certificate
             * chain presented by the TPP server. Only used if using HTTPS; ignored for HTTP.
             * If undefined, the certificate bundle in the cert-manager controller container
             * is used to validate the chain.
             */
            caBundle?: pulumi.Input<string>;
            caBundleSecretRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiTppCaBundleSecretRefPatch>;
            credentialsRef?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerSpecVenafiTppCredentialsRefPatch>;
            /**
             * URL is the base URL for the vedsdk endpoint of the Venafi TPP instance,
             * for example: "https://tpp.example.com/vedsdk".
             */
            url?: pulumi.Input<string>;
        }

        /**
         * Status of the ClusterIssuer. This is set and managed automatically.
         */
        export interface ClusterIssuerStatus {
            acme?: pulumi.Input<inputs.cert_manager.v1.ClusterIssuerStatusAcme>;
            /**
             * List of status conditions to indicate the status of a CertificateRequest.
             * Known condition types are `Ready`.
             */
            conditions?: pulumi.Input<pulumi.Input<inputs.cert_manager.v1.ClusterIssuerStatusConditions>[]>;
        }

        /**
         * ACME specific status options.
         * This field should only be set if the Issuer is configured to use an ACME
         * server to issue certificates.
         */
        export interface ClusterIssuerStatusAcme {
            /**
             * LastPrivateKeyHash is a hash of the private key associated with the latest
             * registered ACME account, in order to track changes made to registered account
             * associated with the Issuer
             */
            lastPrivateKeyHash?: pulumi.Input<string>;
            /**
             * LastRegisteredEmail is the email associated with the latest registered
             * ACME account, in order to track changes made to registered account
             * associated with the  Issuer
             */
            lastRegisteredEmail?: pulumi.Input<string>;
            /**
             * URI is the unique account identifier, which can also be used to retrieve
             * account details from the CA
             */
            uri?: pulumi.Input<string>;
        }

        /**
         * IssuerCondition contains condition information for an Issuer.
         */
        export interface ClusterIssuerStatusConditions {
            /**
             * LastTransitionTime is the timestamp corresponding to the last status
             * change of this condition.
             */
            lastTransitionTime?: pulumi.Input<string>;
            /**
             * Message is a human readable description of the details of the last
             * transition, complementing reason.
             */
            message?: pulumi.Input<string>;
            /**
             * If set, this represents the .metadata.generation that the condition was
             * set based upon.
             * For instance, if .metadata.generation is currently 12, but the
             * .status.condition[x].observedGeneration is 9, the condition is out of date
             * with respect to the current state of the Issuer.
             */
            observedGeneration?: pulumi.Input<number>;
            /**
             * Reason is a brief machine readable explanation for the condition's last
             * transition.
             */
            reason?: pulumi.Input<string>;
            /**
             * Status of the condition, one of (`True`, `False`, `Unknown`).
             */
            status?: pulumi.Input<string>;
            /**
             * Type of the condition, known values are (`Ready`).
             */
            type?: pulumi.Input<string>;
        }

    }
}

export namespace meta {
    export namespace v1 {
        /**
         * ListMeta describes metadata that synthetic resources must have, including lists and various status objects. A resource may have only one of {ObjectMeta, ListMeta}.
         */
        export interface ListMeta {
            /**
             * continue may be set if the user set a limit on the number of items returned, and indicates that the server has more data available. The value is opaque and may be used to issue another request to the endpoint that served this list to retrieve the next set of available objects. Continuing a consistent list may not be possible if the server configuration has changed or more than a few minutes have passed. The resourceVersion field returned when using this continue value will be identical to the value in the first response, unless you have received this token from an error message.
             */
            continue?: pulumi.Input<string>;
            /**
             * remainingItemCount is the number of subsequent items in the list which are not included in this list response. If the list request contained label or field selectors, then the number of remaining items is unknown and the field will be left unset and omitted during serialization. If the list is complete (either because it is not chunking or because this is the last chunk), then there are no more remaining items and this field will be left unset and omitted during serialization. Servers older than v1.15 do not set this field. The intended use of the remainingItemCount is *estimating* the size of a collection. Clients should not rely on the remainingItemCount to be set or to be exact.
             */
            remainingItemCount?: pulumi.Input<number>;
            /**
             * String that identifies the server's internal version of this object that can be used by clients to determine when objects have changed. Value must be treated as opaque by clients and passed unmodified back to the server. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
             */
            resourceVersion?: pulumi.Input<string>;
            /**
             * Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.
             */
            selfLink?: pulumi.Input<string>;
        }

        /**
         * ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource that the fieldset applies to.
         */
        export interface ManagedFieldsEntry {
            /**
             * APIVersion defines the version of this resource that this field set applies to. The format is "group/version" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted.
             */
            apiVersion?: pulumi.Input<string>;
            /**
             * FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: "FieldsV1"
             */
            fieldsType?: pulumi.Input<string>;
            /**
             * FieldsV1 holds the first JSON version format as described in the "FieldsV1" type.
             */
            fieldsV1?: any;
            /**
             * Manager is an identifier of the workflow managing these fields.
             */
            manager?: pulumi.Input<string>;
            /**
             * Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'.
             */
            operation?: pulumi.Input<string>;
            /**
             * Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource.
             */
            subresource?: pulumi.Input<string>;
            /**
             * Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over.
             */
            time?: pulumi.Input<string>;
        }

        /**
         * ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource that the fieldset applies to.
         */
        export interface ManagedFieldsEntryPatch {
            /**
             * APIVersion defines the version of this resource that this field set applies to. The format is "group/version" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted.
             */
            apiVersion?: pulumi.Input<string>;
            /**
             * FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: "FieldsV1"
             */
            fieldsType?: pulumi.Input<string>;
            /**
             * FieldsV1 holds the first JSON version format as described in the "FieldsV1" type.
             */
            fieldsV1?: any;
            /**
             * Manager is an identifier of the workflow managing these fields.
             */
            manager?: pulumi.Input<string>;
            /**
             * Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'.
             */
            operation?: pulumi.Input<string>;
            /**
             * Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource.
             */
            subresource?: pulumi.Input<string>;
            /**
             * Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over.
             */
            time?: pulumi.Input<string>;
        }

        /**
         * ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create.
         */
        export interface ObjectMeta {
            /**
             * Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.
             *
             * Populated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
             */
            creationTimestamp?: pulumi.Input<string>;
            /**
             * Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only.
             */
            deletionGracePeriodSeconds?: pulumi.Input<number>;
            /**
             * DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.
             *
             * Populated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
             */
            deletionTimestamp?: pulumi.Input<string>;
            /**
             * Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list.
             */
            finalizers?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.
             *
             * If this field is specified and the generated name exists, the server will return a 409.
             *
             * Applied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency
             */
            generateName?: pulumi.Input<string>;
            /**
             * A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.
             */
            generation?: pulumi.Input<number>;
            /**
             * Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like "ci-cd". The set of fields is always in the version that the workflow used when modifying the object.
             */
            managedFields?: pulumi.Input<pulumi.Input<inputs.meta.v1.ManagedFieldsEntry>[]>;
            /**
             * Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names
             */
            name?: pulumi.Input<string>;
            /**
             * Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the "default" namespace, but "default" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.
             *
             * Must be a DNS_LABEL. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces
             */
            namespace?: pulumi.Input<string>;
            /**
             * List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller.
             */
            ownerReferences?: pulumi.Input<pulumi.Input<inputs.meta.v1.OwnerReference>[]>;
            /**
             * An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.
             *
             * Populated by the system. Read-only. Value must be treated as opaque by clients and . More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
             */
            resourceVersion?: pulumi.Input<string>;
            /**
             * Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.
             */
            selfLink?: pulumi.Input<string>;
            /**
             * UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.
             *
             * Populated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids
             */
            uid?: pulumi.Input<string>;
        }

        /**
         * ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create.
         */
        export interface ObjectMetaPatch {
            /**
             * Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations
             */
            annotations?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.
             *
             * Populated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
             */
            creationTimestamp?: pulumi.Input<string>;
            /**
             * Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only.
             */
            deletionGracePeriodSeconds?: pulumi.Input<number>;
            /**
             * DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.
             *
             * Populated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
             */
            deletionTimestamp?: pulumi.Input<string>;
            /**
             * Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list.
             */
            finalizers?: pulumi.Input<pulumi.Input<string>[]>;
            /**
             * GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.
             *
             * If this field is specified and the generated name exists, the server will return a 409.
             *
             * Applied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency
             */
            generateName?: pulumi.Input<string>;
            /**
             * A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.
             */
            generation?: pulumi.Input<number>;
            /**
             * Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels
             */
            labels?: pulumi.Input<{[key: string]: pulumi.Input<string>}>;
            /**
             * ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like "ci-cd". The set of fields is always in the version that the workflow used when modifying the object.
             */
            managedFields?: pulumi.Input<pulumi.Input<inputs.meta.v1.ManagedFieldsEntryPatch>[]>;
            /**
             * Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names
             */
            name?: pulumi.Input<string>;
            /**
             * Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the "default" namespace, but "default" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.
             *
             * Must be a DNS_LABEL. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces
             */
            namespace?: pulumi.Input<string>;
            /**
             * List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller.
             */
            ownerReferences?: pulumi.Input<pulumi.Input<inputs.meta.v1.OwnerReferencePatch>[]>;
            /**
             * An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.
             *
             * Populated by the system. Read-only. Value must be treated as opaque by clients and . More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
             */
            resourceVersion?: pulumi.Input<string>;
            /**
             * Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.
             */
            selfLink?: pulumi.Input<string>;
            /**
             * UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.
             *
             * Populated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids
             */
            uid?: pulumi.Input<string>;
        }

        /**
         * OwnerReference contains enough information to let you identify an owning object. An owning object must be in the same namespace as the dependent, or be cluster-scoped, so there is no namespace field.
         */
        export interface OwnerReference {
            /**
             * API version of the referent.
             */
            apiVersion: pulumi.Input<string>;
            /**
             * If true, AND if the owner has the "foregroundDeletion" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs "delete" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned.
             */
            blockOwnerDeletion?: pulumi.Input<boolean>;
            /**
             * If true, this reference points to the managing controller.
             */
            controller?: pulumi.Input<boolean>;
            /**
             * Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
             */
            kind: pulumi.Input<string>;
            /**
             * Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names
             */
            name: pulumi.Input<string>;
            /**
             * UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids
             */
            uid: pulumi.Input<string>;
        }

        /**
         * OwnerReference contains enough information to let you identify an owning object. An owning object must be in the same namespace as the dependent, or be cluster-scoped, so there is no namespace field.
         */
        export interface OwnerReferencePatch {
            /**
             * API version of the referent.
             */
            apiVersion?: pulumi.Input<string>;
            /**
             * If true, AND if the owner has the "foregroundDeletion" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs "delete" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned.
             */
            blockOwnerDeletion?: pulumi.Input<boolean>;
            /**
             * If true, this reference points to the managing controller.
             */
            controller?: pulumi.Input<boolean>;
            /**
             * Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
             */
            kind?: pulumi.Input<string>;
            /**
             * Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names
             */
            name?: pulumi.Input<string>;
            /**
             * UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids
             */
            uid?: pulumi.Input<string>;
        }

    }
}

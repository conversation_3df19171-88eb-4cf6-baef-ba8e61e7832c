apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    helm.sh/resource-policy: keep
    meta.helm.sh/release-name: cert-manager
    meta.helm.sh/release-namespace: cert-manager
  creationTimestamp: "2025-05-26T11:31:46Z"
  generation: 1
  labels:
    app: cert-manager
    app.kubernetes.io/instance: cert-manager
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: cert-manager
    app.kubernetes.io/version: v1.17.1
    helm.sh/chart: cert-manager-v1.17.1
  managedFields:
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:helm.sh/resource-policy: {}
          f:meta.helm.sh/release-name: {}
          f:meta.helm.sh/release-namespace: {}
        f:labels:
          .: {}
          f:app: {}
          f:app.kubernetes.io/instance: {}
          f:app.kubernetes.io/managed-by: {}
          f:app.kubernetes.io/name: {}
          f:app.kubernetes.io/version: {}
          f:helm.sh/chart: {}
      f:spec:
        f:conversion:
          .: {}
          f:strategy: {}
        f:group: {}
        f:names:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:scope: {}
        f:versions: {}
    manager: helm
    operation: Update
    time: "2025-05-26T11:31:46Z"
  - apiVersion: apiextensions.k8s.io/v1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:acceptedNames:
          f:categories: {}
          f:kind: {}
          f:listKind: {}
          f:plural: {}
          f:singular: {}
        f:conditions:
          k:{"type":"Established"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
          k:{"type":"NamesAccepted"}:
            .: {}
            f:lastTransitionTime: {}
            f:message: {}
            f:reason: {}
            f:status: {}
            f:type: {}
    manager: k3s
    operation: Update
    subresource: status
    time: "2025-05-26T11:31:47Z"
  name: clusterissuers.cert-manager.io
  resourceVersion: "4660"
  uid: 28e4a803-dcea-48e1-90d0-6ee25f4a30e5
spec:
  conversion:
    strategy: None
  group: cert-manager.io
  names:
    categories:
    - cert-manager
    kind: ClusterIssuer
    listKind: ClusterIssuerList
    plural: clusterissuers
    singular: clusterissuer
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.conditions[?(@.type=="Ready")].status
      name: Ready
      type: string
    - jsonPath: .status.conditions[?(@.type=="Ready")].message
      name: Status
      priority: 1
      type: string
    - description: CreationTimestamp is a timestamp representing the server time when
        this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC.
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: |-
          A ClusterIssuer represents a certificate issuing authority which can be
          referenced as part of `issuerRef` fields.
          It is similar to an Issuer, however it is cluster-scoped and therefore can
          be referenced by resources that exist in *any* namespace, not just the same
          namespace as the referent.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Desired state of the ClusterIssuer resource.
            properties:
              acme:
                description: |-
                  ACME configures this issuer to communicate with a RFC8555 (ACME) server
                  to obtain signed x509 certificates.
                properties:
                  caBundle:
                    description: |-
                      Base64-encoded bundle of PEM CAs which can be used to validate the certificate
                      chain presented by the ACME server.
                      Mutually exclusive with SkipTLSVerify; prefer using CABundle to prevent various
                      kinds of security vulnerabilities.
                      If CABundle and SkipTLSVerify are unset, the system certificate bundle inside
                      the container is used to validate the TLS connection.
                    format: byte
                    type: string
                  disableAccountKeyGeneration:
                    description: |-
                      Enables or disables generating a new ACME account key.
                      If true, the Issuer resource will *not* request a new account but will expect
                      the account key to be supplied via an existing secret.
                      If false, the cert-manager system will generate a new ACME account key
                      for the Issuer.
                      Defaults to false.
                    type: boolean
                  email:
                    description: |-
                      Email is the email address to be associated with the ACME account.
                      This field is optional, but it is strongly recommended to be set.
                      It will be used to contact you in case of issues with your account or
                      certificates, including expiry notification emails.
                      This field may be updated after the account is initially registered.
                    type: string
                  enableDurationFeature:
                    description: |-
                      Enables requesting a Not After date on certificates that matches the
                      duration of the certificate. This is not supported by all ACME servers
                      like Let's Encrypt. If set to true when the ACME server does not support
                      it, it will create an error on the Order.
                      Defaults to false.
                    type: boolean
                  externalAccountBinding:
                    description: |-
                      ExternalAccountBinding is a reference to a CA external account of the ACME
                      server.
                      If set, upon registration cert-manager will attempt to associate the given
                      external account credentials with the registered ACME account.
                    properties:
                      keyAlgorithm:
                        description: |-
                          Deprecated: keyAlgorithm field exists for historical compatibility
                          reasons and should not be used. The algorithm is now hardcoded to HS256
                          in golang/x/crypto/acme.
                        enum:
                        - HS256
                        - HS384
                        - HS512
                        type: string
                      keyID:
                        description: keyID is the ID of the CA key that the External
                          Account is bound to.
                        type: string
                      keySecretRef:
                        description: |-
                          keySecretRef is a Secret Key Selector referencing a data item in a Kubernetes
                          Secret which holds the symmetric MAC key of the External Account Binding.
                          The `key` is the index string that is paired with the key data in the
                          Secret and should not be confused with the key data itself, or indeed with
                          the External Account Binding keyID above.
                          The secret key stored in the Secret **must** be un-padded, base64 URL
                          encoded data.
                        properties:
                          key:
                            description: |-
                              The key of the entry in the Secret resource's `data` field to be used.
                              Some instances of this field may be defaulted, in others it may be
                              required.
                            type: string
                          name:
                            description: |-
                              Name of the resource being referred to.
                              More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                            type: string
                        required:
                        - name
                        type: object
                    required:
                    - keyID
                    - keySecretRef
                    type: object
                  preferredChain:
                    description: |-
                      PreferredChain is the chain to use if the ACME server outputs multiple.
                      PreferredChain is no guarantee that this one gets delivered by the ACME
                      endpoint.
                      For example, for Let's Encrypt's DST crosssign you would use:
                      "DST Root CA X3" or "ISRG Root X1" for the newer Let's Encrypt root CA.
                      This value picks the first certificate bundle in the combined set of
                      ACME default and alternative chains that has a root-most certificate with
                      this value as its issuer's commonname.
                    maxLength: 64
                    type: string
                  privateKeySecretRef:
                    description: |-
                      PrivateKey is the name of a Kubernetes Secret resource that will be used to
                      store the automatically generated ACME account private key.
                      Optionally, a `key` may be specified to select a specific entry within
                      the named Secret resource.
                      If `key` is not specified, a default of `tls.key` will be used.
                    properties:
                      key:
                        description: |-
                          The key of the entry in the Secret resource's `data` field to be used.
                          Some instances of this field may be defaulted, in others it may be
                          required.
                        type: string
                      name:
                        description: |-
                          Name of the resource being referred to.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                        type: string
                    required:
                    - name
                    type: object
                  server:
                    description: |-
                      Server is the URL used to access the ACME server's 'directory' endpoint.
                      For example, for Let's Encrypt's staging endpoint, you would use:
                      "https://acme-staging-v02.api.letsencrypt.org/directory".
                      Only ACME v2 endpoints (i.e. RFC 8555) are supported.
                    type: string
                  skipTLSVerify:
                    description: |-
                      INSECURE: Enables or disables validation of the ACME server TLS certificate.
                      If true, requests to the ACME server will not have the TLS certificate chain
                      validated.
                      Mutually exclusive with CABundle; prefer using CABundle to prevent various
                      kinds of security vulnerabilities.
                      Only enable this option in development environments.
                      If CABundle and SkipTLSVerify are unset, the system certificate bundle inside
                      the container is used to validate the TLS connection.
                      Defaults to false.
                    type: boolean
                  solvers:
                    description: |-
                      Solvers is a list of challenge solvers that will be used to solve
                      ACME challenges for the matching domains.
                      Solver configurations must be provided in order to obtain certificates
                      from an ACME server.
                      For more information, see: https://cert-manager.io/docs/configuration/acme/
                    items:
                      description: |-
                        An ACMEChallengeSolver describes how to solve ACME challenges for the issuer it is part of.
                        A selector may be provided to use different solving strategies for different DNS names.
                        Only one of HTTP01 or DNS01 must be provided.
                      properties:
                        dns01:
                          description: |-
                            Configures cert-manager to attempt to complete authorizations by
                            performing the DNS01 challenge flow.
                          properties:
                            acmeDNS:
                              description: |-
                                Use the 'ACME DNS' (https://github.com/joohoi/acme-dns) API to manage
                                DNS01 challenge records.
                              properties:
                                accountSecretRef:
                                  description: |-
                                    A reference to a specific 'key' within a Secret resource.
                                    In some instances, `key` is a required field.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                host:
                                  type: string
                              required:
                              - accountSecretRef
                              - host
                              type: object
                            akamai:
                              description: Use the Akamai DNS zone management API
                                to manage DNS01 challenge records.
                              properties:
                                accessTokenSecretRef:
                                  description: |-
                                    A reference to a specific 'key' within a Secret resource.
                                    In some instances, `key` is a required field.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                clientSecretSecretRef:
                                  description: |-
                                    A reference to a specific 'key' within a Secret resource.
                                    In some instances, `key` is a required field.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                clientTokenSecretRef:
                                  description: |-
                                    A reference to a specific 'key' within a Secret resource.
                                    In some instances, `key` is a required field.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                serviceConsumerDomain:
                                  type: string
                              required:
                              - accessTokenSecretRef
                              - clientSecretSecretRef
                              - clientTokenSecretRef
                              - serviceConsumerDomain
                              type: object
                            azureDNS:
                              description: Use the Microsoft Azure DNS API to manage
                                DNS01 challenge records.
                              properties:
                                clientID:
                                  description: |-
                                    Auth: Azure Service Principal:
                                    The ClientID of the Azure Service Principal used to authenticate with Azure DNS.
                                    If set, ClientSecret and TenantID must also be set.
                                  type: string
                                clientSecretSecretRef:
                                  description: |-
                                    Auth: Azure Service Principal:
                                    A reference to a Secret containing the password associated with the Service Principal.
                                    If set, ClientID and TenantID must also be set.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                environment:
                                  description: name of the Azure environment (default
                                    AzurePublicCloud)
                                  enum:
                                  - AzurePublicCloud
                                  - AzureChinaCloud
                                  - AzureGermanCloud
                                  - AzureUSGovernmentCloud
                                  type: string
                                hostedZoneName:
                                  description: name of the DNS zone that should be
                                    used
                                  type: string
                                managedIdentity:
                                  description: |-
                                    Auth: Azure Workload Identity or Azure Managed Service Identity:
                                    Settings to enable Azure Workload Identity or Azure Managed Service Identity
                                    If set, ClientID, ClientSecret and TenantID must not be set.
                                  properties:
                                    clientID:
                                      description: client ID of the managed identity,
                                        can not be used at the same time as resourceID
                                      type: string
                                    resourceID:
                                      description: |-
                                        resource ID of the managed identity, can not be used at the same time as clientID
                                        Cannot be used for Azure Managed Service Identity
                                      type: string
                                    tenantID:
                                      description: tenant ID of the managed identity,
                                        can not be used at the same time as resourceID
                                      type: string
                                  type: object
                                resourceGroupName:
                                  description: resource group the DNS zone is located
                                    in
                                  type: string
                                subscriptionID:
                                  description: ID of the Azure subscription
                                  type: string
                                tenantID:
                                  description: |-
                                    Auth: Azure Service Principal:
                                    The TenantID of the Azure Service Principal used to authenticate with Azure DNS.
                                    If set, ClientID and ClientSecret must also be set.
                                  type: string
                              required:
                              - resourceGroupName
                              - subscriptionID
                              type: object
                            cloudDNS:
                              description: Use the Google Cloud DNS API to manage
                                DNS01 challenge records.
                              properties:
                                hostedZoneName:
                                  description: |-
                                    HostedZoneName is an optional field that tells cert-manager in which
                                    Cloud DNS zone the challenge record has to be created.
                                    If left empty cert-manager will automatically choose a zone.
                                  type: string
                                project:
                                  type: string
                                serviceAccountSecretRef:
                                  description: |-
                                    A reference to a specific 'key' within a Secret resource.
                                    In some instances, `key` is a required field.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                              required:
                              - project
                              type: object
                            cloudflare:
                              description: Use the Cloudflare API to manage DNS01
                                challenge records.
                              properties:
                                apiKeySecretRef:
                                  description: |-
                                    API key to use to authenticate with Cloudflare.
                                    Note: using an API token to authenticate is now the recommended method
                                    as it allows greater control of permissions.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                apiTokenSecretRef:
                                  description: API token used to authenticate with
                                    Cloudflare.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                email:
                                  description: Email of the account, only required
                                    when using API key based authentication.
                                  type: string
                              type: object
                            cnameStrategy:
                              description: |-
                                CNAMEStrategy configures how the DNS01 provider should handle CNAME
                                records when found in DNS zones.
                              enum:
                              - None
                              - Follow
                              type: string
                            digitalocean:
                              description: Use the DigitalOcean DNS API to manage
                                DNS01 challenge records.
                              properties:
                                tokenSecretRef:
                                  description: |-
                                    A reference to a specific 'key' within a Secret resource.
                                    In some instances, `key` is a required field.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                              required:
                              - tokenSecretRef
                              type: object
                            rfc2136:
                              description: |-
                                Use RFC2136 ("Dynamic Updates in the Domain Name System") (https://datatracker.ietf.org/doc/rfc2136/)
                                to manage DNS01 challenge records.
                              properties:
                                nameserver:
                                  description: |-
                                    The IP address or hostname of an authoritative DNS server supporting
                                    RFC2136 in the form host:port. If the host is an IPv6 address it must be
                                    enclosed in square brackets (e.g [2001:db8::1]) ; port is optional.
                                    This field is required.
                                  type: string
                                tsigAlgorithm:
                                  description: |-
                                    The TSIG Algorithm configured in the DNS supporting RFC2136. Used only
                                    when ``tsigSecretSecretRef`` and ``tsigKeyName`` are defined.
                                    Supported values are (case-insensitive): ``HMACMD5`` (default),
                                    ``HMACSHA1``, ``HMACSHA256`` or ``HMACSHA512``.
                                  type: string
                                tsigKeyName:
                                  description: |-
                                    The TSIG Key name configured in the DNS.
                                    If ``tsigSecretSecretRef`` is defined, this field is required.
                                  type: string
                                tsigSecretSecretRef:
                                  description: |-
                                    The name of the secret containing the TSIG value.
                                    If ``tsigKeyName`` is defined, this field is required.
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                              required:
                              - nameserver
                              type: object
                            route53:
                              description: Use the AWS Route53 API to manage DNS01
                                challenge records.
                              properties:
                                accessKeyID:
                                  description: |-
                                    The AccessKeyID is used for authentication.
                                    Cannot be set when SecretAccessKeyID is set.
                                    If neither the Access Key nor Key ID are set, we fall-back to using env
                                    vars, shared credentials file or AWS Instance metadata,
                                    see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
                                  type: string
                                accessKeyIDSecretRef:
                                  description: |-
                                    The SecretAccessKey is used for authentication. If set, pull the AWS
                                    access key ID from a key within a Kubernetes Secret.
                                    Cannot be set when AccessKeyID is set.
                                    If neither the Access Key nor Key ID are set, we fall-back to using env
                                    vars, shared credentials file or AWS Instance metadata,
                                    see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                                auth:
                                  description: Auth configures how cert-manager authenticates.
                                  properties:
                                    kubernetes:
                                      description: |-
                                        Kubernetes authenticates with Route53 using AssumeRoleWithWebIdentity
                                        by passing a bound ServiceAccount token.
                                      properties:
                                        serviceAccountRef:
                                          description: |-
                                            A reference to a service account that will be used to request a bound
                                            token (also known as "projected token"). To use this field, you must
                                            configure an RBAC rule to let cert-manager request a token.
                                          properties:
                                            audiences:
                                              description: |-
                                                TokenAudiences is an optional list of audiences to include in the
                                                token passed to AWS. The default token consisting of the issuer's namespace
                                                and name is always included.
                                                If unset the audience defaults to `sts.amazonaws.com`.
                                              items:
                                                type: string
                                              type: array
                                            name:
                                              description: Name of the ServiceAccount
                                                used to request a token.
                                              type: string
                                          required:
                                          - name
                                          type: object
                                      required:
                                      - serviceAccountRef
                                      type: object
                                  required:
                                  - kubernetes
                                  type: object
                                hostedZoneID:
                                  description: If set, the provider will manage only
                                    this zone in Route53 and will not do a lookup
                                    using the route53:ListHostedZonesByName api call.
                                  type: string
                                region:
                                  description: |-
                                    Override the AWS region.

                                    Route53 is a global service and does not have regional endpoints but the
                                    region specified here (or via environment variables) is used as a hint to
                                    help compute the correct AWS credential scope and partition when it
                                    connects to Route53. See:
                                    - [Amazon Route 53 endpoints and quotas](https://docs.aws.amazon.com/general/latest/gr/r53.html)
                                    - [Global services](https://docs.aws.amazon.com/whitepapers/latest/aws-fault-isolation-boundaries/global-services.html)

                                    If you omit this region field, cert-manager will use the region from
                                    AWS_REGION and AWS_DEFAULT_REGION environment variables, if they are set
                                    in the cert-manager controller Pod.

                                    The `region` field is not needed if you use [IAM Roles for Service Accounts (IRSA)](https://docs.aws.amazon.com/eks/latest/userguide/iam-roles-for-service-accounts.html).
                                    Instead an AWS_REGION environment variable is added to the cert-manager controller Pod by:
                                    [Amazon EKS Pod Identity Webhook](https://github.com/aws/amazon-eks-pod-identity-webhook).
                                    In this case this `region` field value is ignored.

                                    The `region` field is not needed if you use [EKS Pod Identities](https://docs.aws.amazon.com/eks/latest/userguide/pod-identities.html).
                                    Instead an AWS_REGION environment variable is added to the cert-manager controller Pod by:
                                    [Amazon EKS Pod Identity Agent](https://github.com/aws/eks-pod-identity-agent),
                                    In this case this `region` field value is ignored.
                                  type: string
                                role:
                                  description: |-
                                    Role is a Role ARN which the Route53 provider will assume using either the explicit credentials AccessKeyID/SecretAccessKey
                                    or the inferred credentials from environment variables, shared credentials file or AWS Instance metadata
                                  type: string
                                secretAccessKeySecretRef:
                                  description: |-
                                    The SecretAccessKey is used for authentication.
                                    If neither the Access Key nor Key ID are set, we fall-back to using env
                                    vars, shared credentials file or AWS Instance metadata,
                                    see: https://docs.aws.amazon.com/sdk-for-go/v1/developer-guide/configuring-sdk.html#specifying-credentials
                                  properties:
                                    key:
                                      description: |-
                                        The key of the entry in the Secret resource's `data` field to be used.
                                        Some instances of this field may be defaulted, in others it may be
                                        required.
                                      type: string
                                    name:
                                      description: |-
                                        Name of the resource being referred to.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                  required:
                                  - name
                                  type: object
                              type: object
                            webhook:
                              description: |-
                                Configure an external webhook based DNS01 challenge solver to manage
                                DNS01 challenge records.
                              properties:
                                config:
                                  description: |-
                                    Additional configuration that should be passed to the webhook apiserver
                                    when challenges are processed.
                                    This can contain arbitrary JSON data.
                                    Secret values should not be specified in this stanza.
                                    If secret values are needed (e.g. credentials for a DNS service), you
                                    should use a SecretKeySelector to reference a Secret resource.
                                    For details on the schema of this field, consult the webhook provider
                                    implementation's documentation.
                                  x-kubernetes-preserve-unknown-fields: true
                                groupName:
                                  description: |-
                                    The API group name that should be used when POSTing ChallengePayload
                                    resources to the webhook apiserver.
                                    This should be the same as the GroupName specified in the webhook
                                    provider implementation.
                                  type: string
                                solverName:
                                  description: |-
                                    The name of the solver to use, as defined in the webhook provider
                                    implementation.
                                    This will typically be the name of the provider, e.g. 'cloudflare'.
                                  type: string
                              required:
                              - groupName
                              - solverName
                              type: object
                          type: object
                        http01:
                          description: |-
                            Configures cert-manager to attempt to complete authorizations by
                            performing the HTTP01 challenge flow.
                            It is not possible to obtain certificates for wildcard domain names
                            (e.g. `*.example.com`) using the HTTP01 challenge mechanism.
                          properties:
                            gatewayHTTPRoute:
                              description: |-
                                The Gateway API is a sig-network community API that models service networking
                                in Kubernetes (https://gateway-api.sigs.k8s.io/). The Gateway solver will
                                create HTTPRoutes with the specified labels in the same namespace as the challenge.
                                This solver is experimental, and fields / behaviour may change in the future.
                              properties:
                                labels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    Custom labels that will be applied to HTTPRoutes created by cert-manager
                                    while solving HTTP-01 challenges.
                                  type: object
                                parentRefs:
                                  description: |-
                                    When solving an HTTP-01 challenge, cert-manager creates an HTTPRoute.
                                    cert-manager needs to know which parentRefs should be used when creating
                                    the HTTPRoute. Usually, the parentRef references a Gateway. See:
                                    https://gateway-api.sigs.k8s.io/api-types/httproute/#attaching-to-gateways
                                  items:
                                    description: |-
                                      ParentReference identifies an API object (usually a Gateway) that can be considered
                                      a parent of this resource (usually a route). There are two kinds of parent resources
                                      with "Core" support:

                                      * Gateway (Gateway conformance profile)
                                      * Service (Mesh conformance profile, ClusterIP Services only)

                                      This API may be extended in the future to support additional kinds of parent
                                      resources.

                                      The API object must be valid in the cluster; the Group and Kind must
                                      be registered in the cluster for this reference to be valid.
                                    properties:
                                      group:
                                        default: gateway.networking.k8s.io
                                        description: |-
                                          Group is the group of the referent.
                                          When unspecified, "gateway.networking.k8s.io" is inferred.
                                          To set the core API group (such as for a "Service" kind referent),
                                          Group must be explicitly set to "" (empty string).

                                          Support: Core
                                        maxLength: 253
                                        pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                        type: string
                                      kind:
                                        default: Gateway
                                        description: |-
                                          Kind is kind of the referent.

                                          There are two kinds of parent resources with "Core" support:

                                          * Gateway (Gateway conformance profile)
                                          * Service (Mesh conformance profile, ClusterIP Services only)

                                          Support for other resources is Implementation-Specific.
                                        maxLength: 63
                                        minLength: 1
                                        pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                                        type: string
                                      name:
                                        description: |-
                                          Name is the name of the referent.

                                          Support: Core
                                        maxLength: 253
                                        minLength: 1
                                        type: string
                                      namespace:
                                        description: |-
                                          Namespace is the namespace of the referent. When unspecified, this refers
                                          to the local namespace of the Route.

                                          Note that there are specific rules for ParentRefs which cross namespace
                                          boundaries. Cross-namespace references are only valid if they are explicitly
                                          allowed by something in the namespace they are referring to. For example:
                                          Gateway has the AllowedRoutes field, and ReferenceGrant provides a
                                          generic way to enable any other kind of cross-namespace reference.

                                          <gateway:experimental:description>
                                          ParentRefs from a Route to a Service in the same namespace are "producer"
                                          routes, which apply default routing rules to inbound connections from
                                          any namespace to the Service.

                                          ParentRefs from a Route to a Service in a different namespace are
                                          "consumer" routes, and these routing rules are only applied to outbound
                                          connections originating from the same namespace as the Route, for which
                                          the intended destination of the connections are a Service targeted as a
                                          ParentRef of the Route.
                                          </gateway:experimental:description>

                                          Support: Core
                                        maxLength: 63
                                        minLength: 1
                                        pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                                        type: string
                                      port:
                                        description: |-
                                          Port is the network port this Route targets. It can be interpreted
                                          differently based on the type of parent resource.

                                          When the parent resource is a Gateway, this targets all listeners
                                          listening on the specified port that also support this kind of Route(and
                                          select this Route). It's not recommended to set `Port` unless the
                                          networking behaviors specified in a Route must apply to a specific port
                                          as opposed to a listener(s) whose port(s) may be changed. When both Port
                                          and SectionName are specified, the name and port of the selected listener
                                          must match both specified values.

                                          <gateway:experimental:description>
                                          When the parent resource is a Service, this targets a specific port in the
                                          Service spec. When both Port (experimental) and SectionName are specified,
                                          the name and port of the selected port must match both specified values.
                                          </gateway:experimental:description>

                                          Implementations MAY choose to support other parent resources.
                                          Implementations supporting other types of parent resources MUST clearly
                                          document how/if Port is interpreted.

                                          For the purpose of status, an attachment is considered successful as
                                          long as the parent resource accepts it partially. For example, Gateway
                                          listeners can restrict which Routes can attach to them by Route kind,
                                          namespace, or hostname. If 1 of 2 Gateway listeners accept attachment
                                          from the referencing Route, the Route MUST be considered successfully
                                          attached. If no Gateway listeners accept attachment from this Route,
                                          the Route MUST be considered detached from the Gateway.

                                          Support: Extended
                                        format: int32
                                        maximum: 65535
                                        minimum: 1
                                        type: integer
                                      sectionName:
                                        description: |-
                                          SectionName is the name of a section within the target resource. In the
                                          following resources, SectionName is interpreted as the following:

                                          * Gateway: Listener name. When both Port (experimental) and SectionName
                                          are specified, the name and port of the selected listener must match
                                          both specified values.
                                          * Service: Port name. When both Port (experimental) and SectionName
                                          are specified, the name and port of the selected listener must match
                                          both specified values.

                                          Implementations MAY choose to support attaching Routes to other resources.
                                          If that is the case, they MUST clearly document how SectionName is
                                          interpreted.

                                          When unspecified (empty string), this will reference the entire resource.
                                          For the purpose of status, an attachment is considered successful if at
                                          least one section in the parent resource accepts it. For example, Gateway
                                          listeners can restrict which Routes can attach to them by Route kind,
                                          namespace, or hostname. If 1 of 2 Gateway listeners accept attachment from
                                          the referencing Route, the Route MUST be considered successfully
                                          attached. If no Gateway listeners accept attachment from this Route, the
                                          Route MUST be considered detached from the Gateway.

                                          Support: Core
                                        maxLength: 253
                                        minLength: 1
                                        pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                                        type: string
                                    required:
                                    - name
                                    type: object
                                  type: array
                                podTemplate:
                                  description: |-
                                    Optional pod template used to configure the ACME challenge solver pods
                                    used for HTTP01 challenges.
                                  properties:
                                    metadata:
                                      description: |-
                                        ObjectMeta overrides for the pod used to solve HTTP01 challenges.
                                        Only the 'labels' and 'annotations' fields may be set.
                                        If labels or annotations overlap with in-built values, the values here
                                        will override the in-built values.
                                      properties:
                                        annotations:
                                          additionalProperties:
                                            type: string
                                          description: Annotations that should be
                                            added to the created ACME HTTP01 solver
                                            pods.
                                          type: object
                                        labels:
                                          additionalProperties:
                                            type: string
                                          description: Labels that should be added
                                            to the created ACME HTTP01 solver pods.
                                          type: object
                                      type: object
                                    spec:
                                      description: |-
                                        PodSpec defines overrides for the HTTP01 challenge solver pod.
                                        Check ACMEChallengeSolverHTTP01IngressPodSpec to find out currently supported fields.
                                        All other fields will be ignored.
                                      properties:
                                        affinity:
                                          description: If specified, the pod's scheduling
                                            constraints
                                          properties:
                                            nodeAffinity:
                                              description: Describes node affinity
                                                scheduling rules for the pod.
                                              properties:
                                                preferredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    The scheduler will prefer to schedule pods to nodes that satisfy
                                                    the affinity expressions specified by this field, but it may choose
                                                    a node that violates one or more of the expressions. The node that is
                                                    most preferred is the one with the greatest sum of weights, i.e.
                                                    for each node that meets all of the scheduling requirements (resource
                                                    request, requiredDuringScheduling affinity expressions, etc.),
                                                    compute a sum by iterating through the elements of this field and adding
                                                    "weight" to the sum if the node matches the corresponding matchExpressions; the
                                                    node(s) with the highest sum are the most preferred.
                                                  items:
                                                    description: |-
                                                      An empty preferred scheduling term matches all objects with implicit weight 0
                                                      (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
                                                    properties:
                                                      preference:
                                                        description: A node selector
                                                          term, associated with the
                                                          corresponding weight.
                                                        properties:
                                                          matchExpressions:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's labels.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchFields:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's fields.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      weight:
                                                        description: Weight associated
                                                          with matching the corresponding
                                                          nodeSelectorTerm, in the
                                                          range 1-100.
                                                        format: int32
                                                        type: integer
                                                    required:
                                                    - preference
                                                    - weight
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                                requiredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    If the affinity requirements specified by this field are not met at
                                                    scheduling time, the pod will not be scheduled onto the node.
                                                    If the affinity requirements specified by this field cease to be met
                                                    at some point during pod execution (e.g. due to an update), the system
                                                    may or may not try to eventually evict the pod from its node.
                                                  properties:
                                                    nodeSelectorTerms:
                                                      description: Required. A list
                                                        of node selector terms. The
                                                        terms are ORed.
                                                      items:
                                                        description: |-
                                                          A null or empty node selector term matches no objects. The requirements of
                                                          them are ANDed.
                                                          The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
                                                        properties:
                                                          matchExpressions:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's labels.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchFields:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's fields.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      type: array
                                                      x-kubernetes-list-type: atomic
                                                  required:
                                                  - nodeSelectorTerms
                                                  type: object
                                                  x-kubernetes-map-type: atomic
                                              type: object
                                            podAffinity:
                                              description: Describes pod affinity
                                                scheduling rules (e.g. co-locate this
                                                pod in the same node, zone, etc. as
                                                some other pod(s)).
                                              properties:
                                                preferredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    The scheduler will prefer to schedule pods to nodes that satisfy
                                                    the affinity expressions specified by this field, but it may choose
                                                    a node that violates one or more of the expressions. The node that is
                                                    most preferred is the one with the greatest sum of weights, i.e.
                                                    for each node that meets all of the scheduling requirements (resource
                                                    request, requiredDuringScheduling affinity expressions, etc.),
                                                    compute a sum by iterating through the elements of this field and adding
                                                    "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                                                    node(s) with the highest sum are the most preferred.
                                                  items:
                                                    description: The weights of all
                                                      of the matched WeightedPodAffinityTerm
                                                      fields are added per-node to
                                                      find the most preferred node(s)
                                                    properties:
                                                      podAffinityTerm:
                                                        description: Required. A pod
                                                          affinity term, associated
                                                          with the corresponding weight.
                                                        properties:
                                                          labelSelector:
                                                            description: |-
                                                              A label query over a set of resources, in this case pods.
                                                              If it's null, this PodAffinityTerm matches with no Pods.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          matchLabelKeys:
                                                            description: |-
                                                              MatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                              Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          mismatchLabelKeys:
                                                            description: |-
                                                              MismatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                              Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          namespaceSelector:
                                                            description: |-
                                                              A label query over the set of namespaces that the term applies to.
                                                              The term is applied to the union of the namespaces selected by this field
                                                              and the ones listed in the namespaces field.
                                                              null selector and null or empty namespaces list means "this pod's namespace".
                                                              An empty selector ({}) matches all namespaces.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          namespaces:
                                                            description: |-
                                                              namespaces specifies a static list of namespace names that the term applies to.
                                                              The term is applied to the union of the namespaces listed in this field
                                                              and the ones selected by namespaceSelector.
                                                              null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          topologyKey:
                                                            description: |-
                                                              This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                              the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                              whose value of the label with key topologyKey matches that of any node on which any of the
                                                              selected pods is running.
                                                              Empty topologyKey is not allowed.
                                                            type: string
                                                        required:
                                                        - topologyKey
                                                        type: object
                                                      weight:
                                                        description: |-
                                                          weight associated with matching the corresponding podAffinityTerm,
                                                          in the range 1-100.
                                                        format: int32
                                                        type: integer
                                                    required:
                                                    - podAffinityTerm
                                                    - weight
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                                requiredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    If the affinity requirements specified by this field are not met at
                                                    scheduling time, the pod will not be scheduled onto the node.
                                                    If the affinity requirements specified by this field cease to be met
                                                    at some point during pod execution (e.g. due to a pod label update), the
                                                    system may or may not try to eventually evict the pod from its node.
                                                    When there are multiple elements, the lists of nodes corresponding to each
                                                    podAffinityTerm are intersected, i.e. all terms must be satisfied.
                                                  items:
                                                    description: |-
                                                      Defines a set of pods (namely those matching the labelSelector
                                                      relative to the given namespace(s)) that this pod should be
                                                      co-located (affinity) or not co-located (anti-affinity) with,
                                                      where co-located is defined as running on a node whose value of
                                                      the label with key <topologyKey> matches that of any node on which
                                                      a pod of the set of pods is running
                                                    properties:
                                                      labelSelector:
                                                        description: |-
                                                          A label query over a set of resources, in this case pods.
                                                          If it's null, this PodAffinityTerm matches with no Pods.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      matchLabelKeys:
                                                        description: |-
                                                          MatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                          Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      mismatchLabelKeys:
                                                        description: |-
                                                          MismatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                          Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      namespaceSelector:
                                                        description: |-
                                                          A label query over the set of namespaces that the term applies to.
                                                          The term is applied to the union of the namespaces selected by this field
                                                          and the ones listed in the namespaces field.
                                                          null selector and null or empty namespaces list means "this pod's namespace".
                                                          An empty selector ({}) matches all namespaces.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      namespaces:
                                                        description: |-
                                                          namespaces specifies a static list of namespace names that the term applies to.
                                                          The term is applied to the union of the namespaces listed in this field
                                                          and the ones selected by namespaceSelector.
                                                          null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      topologyKey:
                                                        description: |-
                                                          This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                          the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                          whose value of the label with key topologyKey matches that of any node on which any of the
                                                          selected pods is running.
                                                          Empty topologyKey is not allowed.
                                                        type: string
                                                    required:
                                                    - topologyKey
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                              type: object
                                            podAntiAffinity:
                                              description: Describes pod anti-affinity
                                                scheduling rules (e.g. avoid putting
                                                this pod in the same node, zone, etc.
                                                as some other pod(s)).
                                              properties:
                                                preferredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    The scheduler will prefer to schedule pods to nodes that satisfy
                                                    the anti-affinity expressions specified by this field, but it may choose
                                                    a node that violates one or more of the expressions. The node that is
                                                    most preferred is the one with the greatest sum of weights, i.e.
                                                    for each node that meets all of the scheduling requirements (resource
                                                    request, requiredDuringScheduling anti-affinity expressions, etc.),
                                                    compute a sum by iterating through the elements of this field and adding
                                                    "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                                                    node(s) with the highest sum are the most preferred.
                                                  items:
                                                    description: The weights of all
                                                      of the matched WeightedPodAffinityTerm
                                                      fields are added per-node to
                                                      find the most preferred node(s)
                                                    properties:
                                                      podAffinityTerm:
                                                        description: Required. A pod
                                                          affinity term, associated
                                                          with the corresponding weight.
                                                        properties:
                                                          labelSelector:
                                                            description: |-
                                                              A label query over a set of resources, in this case pods.
                                                              If it's null, this PodAffinityTerm matches with no Pods.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          matchLabelKeys:
                                                            description: |-
                                                              MatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                              Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          mismatchLabelKeys:
                                                            description: |-
                                                              MismatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                              Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          namespaceSelector:
                                                            description: |-
                                                              A label query over the set of namespaces that the term applies to.
                                                              The term is applied to the union of the namespaces selected by this field
                                                              and the ones listed in the namespaces field.
                                                              null selector and null or empty namespaces list means "this pod's namespace".
                                                              An empty selector ({}) matches all namespaces.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          namespaces:
                                                            description: |-
                                                              namespaces specifies a static list of namespace names that the term applies to.
                                                              The term is applied to the union of the namespaces listed in this field
                                                              and the ones selected by namespaceSelector.
                                                              null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          topologyKey:
                                                            description: |-
                                                              This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                              the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                              whose value of the label with key topologyKey matches that of any node on which any of the
                                                              selected pods is running.
                                                              Empty topologyKey is not allowed.
                                                            type: string
                                                        required:
                                                        - topologyKey
                                                        type: object
                                                      weight:
                                                        description: |-
                                                          weight associated with matching the corresponding podAffinityTerm,
                                                          in the range 1-100.
                                                        format: int32
                                                        type: integer
                                                    required:
                                                    - podAffinityTerm
                                                    - weight
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                                requiredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    If the anti-affinity requirements specified by this field are not met at
                                                    scheduling time, the pod will not be scheduled onto the node.
                                                    If the anti-affinity requirements specified by this field cease to be met
                                                    at some point during pod execution (e.g. due to a pod label update), the
                                                    system may or may not try to eventually evict the pod from its node.
                                                    When there are multiple elements, the lists of nodes corresponding to each
                                                    podAffinityTerm are intersected, i.e. all terms must be satisfied.
                                                  items:
                                                    description: |-
                                                      Defines a set of pods (namely those matching the labelSelector
                                                      relative to the given namespace(s)) that this pod should be
                                                      co-located (affinity) or not co-located (anti-affinity) with,
                                                      where co-located is defined as running on a node whose value of
                                                      the label with key <topologyKey> matches that of any node on which
                                                      a pod of the set of pods is running
                                                    properties:
                                                      labelSelector:
                                                        description: |-
                                                          A label query over a set of resources, in this case pods.
                                                          If it's null, this PodAffinityTerm matches with no Pods.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      matchLabelKeys:
                                                        description: |-
                                                          MatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                          Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      mismatchLabelKeys:
                                                        description: |-
                                                          MismatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                          Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      namespaceSelector:
                                                        description: |-
                                                          A label query over the set of namespaces that the term applies to.
                                                          The term is applied to the union of the namespaces selected by this field
                                                          and the ones listed in the namespaces field.
                                                          null selector and null or empty namespaces list means "this pod's namespace".
                                                          An empty selector ({}) matches all namespaces.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      namespaces:
                                                        description: |-
                                                          namespaces specifies a static list of namespace names that the term applies to.
                                                          The term is applied to the union of the namespaces listed in this field
                                                          and the ones selected by namespaceSelector.
                                                          null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      topologyKey:
                                                        description: |-
                                                          This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                          the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                          whose value of the label with key topologyKey matches that of any node on which any of the
                                                          selected pods is running.
                                                          Empty topologyKey is not allowed.
                                                        type: string
                                                    required:
                                                    - topologyKey
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                              type: object
                                          type: object
                                        imagePullSecrets:
                                          description: If specified, the pod's imagePullSecrets
                                          items:
                                            description: |-
                                              LocalObjectReference contains enough information to let you locate the
                                              referenced object inside the same namespace.
                                            properties:
                                              name:
                                                default: ""
                                                description: |-
                                                  Name of the referent.
                                                  This field is effectively required, but due to backwards compatibility is
                                                  allowed to be empty. Instances of this type with an empty value here are
                                                  almost certainly wrong.
                                                  More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                                type: string
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          type: array
                                        nodeSelector:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            NodeSelector is a selector which must be true for the pod to fit on a node.
                                            Selector which must match a node's labels for the pod to be scheduled on that node.
                                            More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
                                          type: object
                                        priorityClassName:
                                          description: If specified, the pod's priorityClassName.
                                          type: string
                                        securityContext:
                                          description: If specified, the pod's security
                                            context
                                          properties:
                                            fsGroup:
                                              description: |-
                                                A special supplemental group that applies to all containers in a pod.
                                                Some volume types allow the Kubelet to change the ownership of that volume
                                                to be owned by the pod:

                                                1. The owning GID will be the FSGroup
                                                2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
                                                3. The permission bits are OR'd with rw-rw----

                                                If unset, the Kubelet will not modify the ownership and permissions of any volume.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              format: int64
                                              type: integer
                                            fsGroupChangePolicy:
                                              description: |-
                                                fsGroupChangePolicy defines behavior of changing ownership and permission of the volume
                                                before being exposed inside Pod. This field will only apply to
                                                volume types which support fsGroup based ownership(and permissions).
                                                It will have no effect on ephemeral volume types such as: secret, configmaps
                                                and emptydir.
                                                Valid values are "OnRootMismatch" and "Always". If not specified, "Always" is used.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              type: string
                                            runAsGroup:
                                              description: |-
                                                The GID to run the entrypoint of the container process.
                                                Uses runtime default if unset.
                                                May also be set in SecurityContext.  If set in both SecurityContext and
                                                PodSecurityContext, the value specified in SecurityContext takes precedence
                                                for that container.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              format: int64
                                              type: integer
                                            runAsNonRoot:
                                              description: |-
                                                Indicates that the container must run as a non-root user.
                                                If true, the Kubelet will validate the image at runtime to ensure that it
                                                does not run as UID 0 (root) and fail to start the container if it does.
                                                If unset or false, no such validation will be performed.
                                                May also be set in SecurityContext.  If set in both SecurityContext and
                                                PodSecurityContext, the value specified in SecurityContext takes precedence.
                                              type: boolean
                                            runAsUser:
                                              description: |-
                                                The UID to run the entrypoint of the container process.
                                                Defaults to user specified in image metadata if unspecified.
                                                May also be set in SecurityContext.  If set in both SecurityContext and
                                                PodSecurityContext, the value specified in SecurityContext takes precedence
                                                for that container.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              format: int64
                                              type: integer
                                            seLinuxOptions:
                                              description: |-
                                                The SELinux context to be applied to all containers.
                                                If unspecified, the container runtime will allocate a random SELinux context for each
                                                container.  May also be set in SecurityContext.  If set in
                                                both SecurityContext and PodSecurityContext, the value specified in SecurityContext
                                                takes precedence for that container.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              properties:
                                                level:
                                                  description: Level is SELinux level
                                                    label that applies to the container.
                                                  type: string
                                                role:
                                                  description: Role is a SELinux role
                                                    label that applies to the container.
                                                  type: string
                                                type:
                                                  description: Type is a SELinux type
                                                    label that applies to the container.
                                                  type: string
                                                user:
                                                  description: User is a SELinux user
                                                    label that applies to the container.
                                                  type: string
                                              type: object
                                            seccompProfile:
                                              description: |-
                                                The seccomp options to use by the containers in this pod.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              properties:
                                                localhostProfile:
                                                  description: |-
                                                    localhostProfile indicates a profile defined in a file on the node should be used.
                                                    The profile must be preconfigured on the node to work.
                                                    Must be a descending path, relative to the kubelet's configured seccomp profile location.
                                                    Must be set if type is "Localhost". Must NOT be set for any other type.
                                                  type: string
                                                type:
                                                  description: |-
                                                    type indicates which kind of seccomp profile will be applied.
                                                    Valid options are:

                                                    Localhost - a profile defined in a file on the node should be used.
                                                    RuntimeDefault - the container runtime default profile should be used.
                                                    Unconfined - no profile should be applied.
                                                  type: string
                                              required:
                                              - type
                                              type: object
                                            supplementalGroups:
                                              description: |-
                                                A list of groups applied to the first process run in each container, in addition
                                                to the container's primary GID, the fsGroup (if specified), and group memberships
                                                defined in the container image for the uid of the container process. If unspecified,
                                                no additional groups are added to any container. Note that group memberships
                                                defined in the container image for the uid of the container process are still effective,
                                                even if they are not included in this list.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              items:
                                                format: int64
                                                type: integer
                                              type: array
                                            sysctls:
                                              description: |-
                                                Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
                                                sysctls (by the container runtime) might fail to launch.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              items:
                                                description: Sysctl defines a kernel
                                                  parameter to be set
                                                properties:
                                                  name:
                                                    description: Name of a property
                                                      to set
                                                    type: string
                                                  value:
                                                    description: Value of a property
                                                      to set
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                          type: object
                                        serviceAccountName:
                                          description: If specified, the pod's service
                                            account
                                          type: string
                                        tolerations:
                                          description: If specified, the pod's tolerations.
                                          items:
                                            description: |-
                                              The pod this Toleration is attached to tolerates any taint that matches
                                              the triple <key,value,effect> using the matching operator <operator>.
                                            properties:
                                              effect:
                                                description: |-
                                                  Effect indicates the taint effect to match. Empty means match all taint effects.
                                                  When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
                                                type: string
                                              key:
                                                description: |-
                                                  Key is the taint key that the toleration applies to. Empty means match all taint keys.
                                                  If the key is empty, operator must be Exists; this combination means to match all values and all keys.
                                                type: string
                                              operator:
                                                description: |-
                                                  Operator represents a key's relationship to the value.
                                                  Valid operators are Exists and Equal. Defaults to Equal.
                                                  Exists is equivalent to wildcard for value, so that a pod can
                                                  tolerate all taints of a particular category.
                                                type: string
                                              tolerationSeconds:
                                                description: |-
                                                  TolerationSeconds represents the period of time the toleration (which must be
                                                  of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
                                                  it is not set, which means tolerate the taint forever (do not evict). Zero and
                                                  negative values will be treated as 0 (evict immediately) by the system.
                                                format: int64
                                                type: integer
                                              value:
                                                description: |-
                                                  Value is the taint value the toleration matches to.
                                                  If the operator is Exists, the value should be empty, otherwise just a regular string.
                                                type: string
                                            type: object
                                          type: array
                                      type: object
                                  type: object
                                serviceType:
                                  description: |-
                                    Optional service type for Kubernetes solver service. Supported values
                                    are NodePort or ClusterIP. If unset, defaults to NodePort.
                                  type: string
                              type: object
                            ingress:
                              description: |-
                                The ingress based HTTP01 challenge solver will solve challenges by
                                creating or modifying Ingress resources in order to route requests for
                                '/.well-known/acme-challenge/XYZ' to 'challenge solver' pods that are
                                provisioned by cert-manager for each Challenge to be completed.
                              properties:
                                class:
                                  description: |-
                                    This field configures the annotation `kubernetes.io/ingress.class` when
                                    creating Ingress resources to solve ACME challenges that use this
                                    challenge solver. Only one of `class`, `name` or `ingressClassName` may
                                    be specified.
                                  type: string
                                ingressClassName:
                                  description: |-
                                    This field configures the field `ingressClassName` on the created Ingress
                                    resources used to solve ACME challenges that use this challenge solver.
                                    This is the recommended way of configuring the ingress class. Only one of
                                    `class`, `name` or `ingressClassName` may be specified.
                                  type: string
                                ingressTemplate:
                                  description: |-
                                    Optional ingress template used to configure the ACME challenge solver
                                    ingress used for HTTP01 challenges.
                                  properties:
                                    metadata:
                                      description: |-
                                        ObjectMeta overrides for the ingress used to solve HTTP01 challenges.
                                        Only the 'labels' and 'annotations' fields may be set.
                                        If labels or annotations overlap with in-built values, the values here
                                        will override the in-built values.
                                      properties:
                                        annotations:
                                          additionalProperties:
                                            type: string
                                          description: Annotations that should be
                                            added to the created ACME HTTP01 solver
                                            ingress.
                                          type: object
                                        labels:
                                          additionalProperties:
                                            type: string
                                          description: Labels that should be added
                                            to the created ACME HTTP01 solver ingress.
                                          type: object
                                      type: object
                                  type: object
                                name:
                                  description: |-
                                    The name of the ingress resource that should have ACME challenge solving
                                    routes inserted into it in order to solve HTTP01 challenges.
                                    This is typically used in conjunction with ingress controllers like
                                    ingress-gce, which maintains a 1:1 mapping between external IPs and
                                    ingress resources. Only one of `class`, `name` or `ingressClassName` may
                                    be specified.
                                  type: string
                                podTemplate:
                                  description: |-
                                    Optional pod template used to configure the ACME challenge solver pods
                                    used for HTTP01 challenges.
                                  properties:
                                    metadata:
                                      description: |-
                                        ObjectMeta overrides for the pod used to solve HTTP01 challenges.
                                        Only the 'labels' and 'annotations' fields may be set.
                                        If labels or annotations overlap with in-built values, the values here
                                        will override the in-built values.
                                      properties:
                                        annotations:
                                          additionalProperties:
                                            type: string
                                          description: Annotations that should be
                                            added to the created ACME HTTP01 solver
                                            pods.
                                          type: object
                                        labels:
                                          additionalProperties:
                                            type: string
                                          description: Labels that should be added
                                            to the created ACME HTTP01 solver pods.
                                          type: object
                                      type: object
                                    spec:
                                      description: |-
                                        PodSpec defines overrides for the HTTP01 challenge solver pod.
                                        Check ACMEChallengeSolverHTTP01IngressPodSpec to find out currently supported fields.
                                        All other fields will be ignored.
                                      properties:
                                        affinity:
                                          description: If specified, the pod's scheduling
                                            constraints
                                          properties:
                                            nodeAffinity:
                                              description: Describes node affinity
                                                scheduling rules for the pod.
                                              properties:
                                                preferredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    The scheduler will prefer to schedule pods to nodes that satisfy
                                                    the affinity expressions specified by this field, but it may choose
                                                    a node that violates one or more of the expressions. The node that is
                                                    most preferred is the one with the greatest sum of weights, i.e.
                                                    for each node that meets all of the scheduling requirements (resource
                                                    request, requiredDuringScheduling affinity expressions, etc.),
                                                    compute a sum by iterating through the elements of this field and adding
                                                    "weight" to the sum if the node matches the corresponding matchExpressions; the
                                                    node(s) with the highest sum are the most preferred.
                                                  items:
                                                    description: |-
                                                      An empty preferred scheduling term matches all objects with implicit weight 0
                                                      (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).
                                                    properties:
                                                      preference:
                                                        description: A node selector
                                                          term, associated with the
                                                          corresponding weight.
                                                        properties:
                                                          matchExpressions:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's labels.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchFields:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's fields.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      weight:
                                                        description: Weight associated
                                                          with matching the corresponding
                                                          nodeSelectorTerm, in the
                                                          range 1-100.
                                                        format: int32
                                                        type: integer
                                                    required:
                                                    - preference
                                                    - weight
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                                requiredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    If the affinity requirements specified by this field are not met at
                                                    scheduling time, the pod will not be scheduled onto the node.
                                                    If the affinity requirements specified by this field cease to be met
                                                    at some point during pod execution (e.g. due to an update), the system
                                                    may or may not try to eventually evict the pod from its node.
                                                  properties:
                                                    nodeSelectorTerms:
                                                      description: Required. A list
                                                        of node selector terms. The
                                                        terms are ORed.
                                                      items:
                                                        description: |-
                                                          A null or empty node selector term matches no objects. The requirements of
                                                          them are ANDed.
                                                          The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
                                                        properties:
                                                          matchExpressions:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's labels.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchFields:
                                                            description: A list of
                                                              node selector requirements
                                                              by node's fields.
                                                            items:
                                                              description: |-
                                                                A node selector requirement is a selector that contains values, a key, and an operator
                                                                that relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: The
                                                                    label key that
                                                                    the selector applies
                                                                    to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    Represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    An array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. If the operator is Gt or Lt, the values
                                                                    array must have a single element, which will be interpreted as an integer.
                                                                    This array is replaced during a strategic merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      type: array
                                                      x-kubernetes-list-type: atomic
                                                  required:
                                                  - nodeSelectorTerms
                                                  type: object
                                                  x-kubernetes-map-type: atomic
                                              type: object
                                            podAffinity:
                                              description: Describes pod affinity
                                                scheduling rules (e.g. co-locate this
                                                pod in the same node, zone, etc. as
                                                some other pod(s)).
                                              properties:
                                                preferredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    The scheduler will prefer to schedule pods to nodes that satisfy
                                                    the affinity expressions specified by this field, but it may choose
                                                    a node that violates one or more of the expressions. The node that is
                                                    most preferred is the one with the greatest sum of weights, i.e.
                                                    for each node that meets all of the scheduling requirements (resource
                                                    request, requiredDuringScheduling affinity expressions, etc.),
                                                    compute a sum by iterating through the elements of this field and adding
                                                    "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                                                    node(s) with the highest sum are the most preferred.
                                                  items:
                                                    description: The weights of all
                                                      of the matched WeightedPodAffinityTerm
                                                      fields are added per-node to
                                                      find the most preferred node(s)
                                                    properties:
                                                      podAffinityTerm:
                                                        description: Required. A pod
                                                          affinity term, associated
                                                          with the corresponding weight.
                                                        properties:
                                                          labelSelector:
                                                            description: |-
                                                              A label query over a set of resources, in this case pods.
                                                              If it's null, this PodAffinityTerm matches with no Pods.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          matchLabelKeys:
                                                            description: |-
                                                              MatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                              Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          mismatchLabelKeys:
                                                            description: |-
                                                              MismatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                              Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          namespaceSelector:
                                                            description: |-
                                                              A label query over the set of namespaces that the term applies to.
                                                              The term is applied to the union of the namespaces selected by this field
                                                              and the ones listed in the namespaces field.
                                                              null selector and null or empty namespaces list means "this pod's namespace".
                                                              An empty selector ({}) matches all namespaces.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          namespaces:
                                                            description: |-
                                                              namespaces specifies a static list of namespace names that the term applies to.
                                                              The term is applied to the union of the namespaces listed in this field
                                                              and the ones selected by namespaceSelector.
                                                              null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          topologyKey:
                                                            description: |-
                                                              This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                              the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                              whose value of the label with key topologyKey matches that of any node on which any of the
                                                              selected pods is running.
                                                              Empty topologyKey is not allowed.
                                                            type: string
                                                        required:
                                                        - topologyKey
                                                        type: object
                                                      weight:
                                                        description: |-
                                                          weight associated with matching the corresponding podAffinityTerm,
                                                          in the range 1-100.
                                                        format: int32
                                                        type: integer
                                                    required:
                                                    - podAffinityTerm
                                                    - weight
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                                requiredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    If the affinity requirements specified by this field are not met at
                                                    scheduling time, the pod will not be scheduled onto the node.
                                                    If the affinity requirements specified by this field cease to be met
                                                    at some point during pod execution (e.g. due to a pod label update), the
                                                    system may or may not try to eventually evict the pod from its node.
                                                    When there are multiple elements, the lists of nodes corresponding to each
                                                    podAffinityTerm are intersected, i.e. all terms must be satisfied.
                                                  items:
                                                    description: |-
                                                      Defines a set of pods (namely those matching the labelSelector
                                                      relative to the given namespace(s)) that this pod should be
                                                      co-located (affinity) or not co-located (anti-affinity) with,
                                                      where co-located is defined as running on a node whose value of
                                                      the label with key <topologyKey> matches that of any node on which
                                                      a pod of the set of pods is running
                                                    properties:
                                                      labelSelector:
                                                        description: |-
                                                          A label query over a set of resources, in this case pods.
                                                          If it's null, this PodAffinityTerm matches with no Pods.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      matchLabelKeys:
                                                        description: |-
                                                          MatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                          Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      mismatchLabelKeys:
                                                        description: |-
                                                          MismatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                          Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      namespaceSelector:
                                                        description: |-
                                                          A label query over the set of namespaces that the term applies to.
                                                          The term is applied to the union of the namespaces selected by this field
                                                          and the ones listed in the namespaces field.
                                                          null selector and null or empty namespaces list means "this pod's namespace".
                                                          An empty selector ({}) matches all namespaces.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      namespaces:
                                                        description: |-
                                                          namespaces specifies a static list of namespace names that the term applies to.
                                                          The term is applied to the union of the namespaces listed in this field
                                                          and the ones selected by namespaceSelector.
                                                          null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      topologyKey:
                                                        description: |-
                                                          This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                          the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                          whose value of the label with key topologyKey matches that of any node on which any of the
                                                          selected pods is running.
                                                          Empty topologyKey is not allowed.
                                                        type: string
                                                    required:
                                                    - topologyKey
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                              type: object
                                            podAntiAffinity:
                                              description: Describes pod anti-affinity
                                                scheduling rules (e.g. avoid putting
                                                this pod in the same node, zone, etc.
                                                as some other pod(s)).
                                              properties:
                                                preferredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    The scheduler will prefer to schedule pods to nodes that satisfy
                                                    the anti-affinity expressions specified by this field, but it may choose
                                                    a node that violates one or more of the expressions. The node that is
                                                    most preferred is the one with the greatest sum of weights, i.e.
                                                    for each node that meets all of the scheduling requirements (resource
                                                    request, requiredDuringScheduling anti-affinity expressions, etc.),
                                                    compute a sum by iterating through the elements of this field and adding
                                                    "weight" to the sum if the node has pods which matches the corresponding podAffinityTerm; the
                                                    node(s) with the highest sum are the most preferred.
                                                  items:
                                                    description: The weights of all
                                                      of the matched WeightedPodAffinityTerm
                                                      fields are added per-node to
                                                      find the most preferred node(s)
                                                    properties:
                                                      podAffinityTerm:
                                                        description: Required. A pod
                                                          affinity term, associated
                                                          with the corresponding weight.
                                                        properties:
                                                          labelSelector:
                                                            description: |-
                                                              A label query over a set of resources, in this case pods.
                                                              If it's null, this PodAffinityTerm matches with no Pods.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          matchLabelKeys:
                                                            description: |-
                                                              MatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                              Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          mismatchLabelKeys:
                                                            description: |-
                                                              MismatchLabelKeys is a set of pod label keys to select which pods will
                                                              be taken into consideration. The keys are used to lookup values from the
                                                              incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                              to select the group of existing pods which pods will be taken into consideration
                                                              for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                              pod labels will be ignored. The default value is empty.
                                                              The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                              Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                              This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          namespaceSelector:
                                                            description: |-
                                                              A label query over the set of namespaces that the term applies to.
                                                              The term is applied to the union of the namespaces selected by this field
                                                              and the ones listed in the namespaces field.
                                                              null selector and null or empty namespaces list means "this pod's namespace".
                                                              An empty selector ({}) matches all namespaces.
                                                            properties:
                                                              matchExpressions:
                                                                description: matchExpressions
                                                                  is a list of label
                                                                  selector requirements.
                                                                  The requirements
                                                                  are ANDed.
                                                                items:
                                                                  description: |-
                                                                    A label selector requirement is a selector that contains values, a key, and an operator that
                                                                    relates the key and values.
                                                                  properties:
                                                                    key:
                                                                      description: key
                                                                        is the label
                                                                        key that the
                                                                        selector applies
                                                                        to.
                                                                      type: string
                                                                    operator:
                                                                      description: |-
                                                                        operator represents a key's relationship to a set of values.
                                                                        Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                      type: string
                                                                    values:
                                                                      description: |-
                                                                        values is an array of string values. If the operator is In or NotIn,
                                                                        the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                        the values array must be empty. This array is replaced during a strategic
                                                                        merge patch.
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                      x-kubernetes-list-type: atomic
                                                                  required:
                                                                  - key
                                                                  - operator
                                                                  type: object
                                                                type: array
                                                                x-kubernetes-list-type: atomic
                                                              matchLabels:
                                                                additionalProperties:
                                                                  type: string
                                                                description: |-
                                                                  matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                                  map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                                  operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                                type: object
                                                            type: object
                                                            x-kubernetes-map-type: atomic
                                                          namespaces:
                                                            description: |-
                                                              namespaces specifies a static list of namespace names that the term applies to.
                                                              The term is applied to the union of the namespaces listed in this field
                                                              and the ones selected by namespaceSelector.
                                                              null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                            items:
                                                              type: string
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          topologyKey:
                                                            description: |-
                                                              This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                              the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                              whose value of the label with key topologyKey matches that of any node on which any of the
                                                              selected pods is running.
                                                              Empty topologyKey is not allowed.
                                                            type: string
                                                        required:
                                                        - topologyKey
                                                        type: object
                                                      weight:
                                                        description: |-
                                                          weight associated with matching the corresponding podAffinityTerm,
                                                          in the range 1-100.
                                                        format: int32
                                                        type: integer
                                                    required:
                                                    - podAffinityTerm
                                                    - weight
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                                requiredDuringSchedulingIgnoredDuringExecution:
                                                  description: |-
                                                    If the anti-affinity requirements specified by this field are not met at
                                                    scheduling time, the pod will not be scheduled onto the node.
                                                    If the anti-affinity requirements specified by this field cease to be met
                                                    at some point during pod execution (e.g. due to a pod label update), the
                                                    system may or may not try to eventually evict the pod from its node.
                                                    When there are multiple elements, the lists of nodes corresponding to each
                                                    podAffinityTerm are intersected, i.e. all terms must be satisfied.
                                                  items:
                                                    description: |-
                                                      Defines a set of pods (namely those matching the labelSelector
                                                      relative to the given namespace(s)) that this pod should be
                                                      co-located (affinity) or not co-located (anti-affinity) with,
                                                      where co-located is defined as running on a node whose value of
                                                      the label with key <topologyKey> matches that of any node on which
                                                      a pod of the set of pods is running
                                                    properties:
                                                      labelSelector:
                                                        description: |-
                                                          A label query over a set of resources, in this case pods.
                                                          If it's null, this PodAffinityTerm matches with no Pods.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      matchLabelKeys:
                                                        description: |-
                                                          MatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key in (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both matchLabelKeys and labelSelector.
                                                          Also, matchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      mismatchLabelKeys:
                                                        description: |-
                                                          MismatchLabelKeys is a set of pod label keys to select which pods will
                                                          be taken into consideration. The keys are used to lookup values from the
                                                          incoming pod labels, those key-value labels are merged with `labelSelector` as `key notin (value)`
                                                          to select the group of existing pods which pods will be taken into consideration
                                                          for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming
                                                          pod labels will be ignored. The default value is empty.
                                                          The same key is forbidden to exist in both mismatchLabelKeys and labelSelector.
                                                          Also, mismatchLabelKeys cannot be set when labelSelector isn't set.
                                                          This is a beta field and requires enabling MatchLabelKeysInPodAffinity feature gate (enabled by default).
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      namespaceSelector:
                                                        description: |-
                                                          A label query over the set of namespaces that the term applies to.
                                                          The term is applied to the union of the namespaces selected by this field
                                                          and the ones listed in the namespaces field.
                                                          null selector and null or empty namespaces list means "this pod's namespace".
                                                          An empty selector ({}) matches all namespaces.
                                                        properties:
                                                          matchExpressions:
                                                            description: matchExpressions
                                                              is a list of label selector
                                                              requirements. The requirements
                                                              are ANDed.
                                                            items:
                                                              description: |-
                                                                A label selector requirement is a selector that contains values, a key, and an operator that
                                                                relates the key and values.
                                                              properties:
                                                                key:
                                                                  description: key
                                                                    is the label key
                                                                    that the selector
                                                                    applies to.
                                                                  type: string
                                                                operator:
                                                                  description: |-
                                                                    operator represents a key's relationship to a set of values.
                                                                    Valid operators are In, NotIn, Exists and DoesNotExist.
                                                                  type: string
                                                                values:
                                                                  description: |-
                                                                    values is an array of string values. If the operator is In or NotIn,
                                                                    the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                                    the values array must be empty. This array is replaced during a strategic
                                                                    merge patch.
                                                                  items:
                                                                    type: string
                                                                  type: array
                                                                  x-kubernetes-list-type: atomic
                                                              required:
                                                              - key
                                                              - operator
                                                              type: object
                                                            type: array
                                                            x-kubernetes-list-type: atomic
                                                          matchLabels:
                                                            additionalProperties:
                                                              type: string
                                                            description: |-
                                                              matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                                              map is equivalent to an element of matchExpressions, whose key field is "key", the
                                                              operator is "In", and the values array contains only "value". The requirements are ANDed.
                                                            type: object
                                                        type: object
                                                        x-kubernetes-map-type: atomic
                                                      namespaces:
                                                        description: |-
                                                          namespaces specifies a static list of namespace names that the term applies to.
                                                          The term is applied to the union of the namespaces listed in this field
                                                          and the ones selected by namespaceSelector.
                                                          null or empty namespaces list and null namespaceSelector means "this pod's namespace".
                                                        items:
                                                          type: string
                                                        type: array
                                                        x-kubernetes-list-type: atomic
                                                      topologyKey:
                                                        description: |-
                                                          This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching
                                                          the labelSelector in the specified namespaces, where co-located is defined as running on a node
                                                          whose value of the label with key topologyKey matches that of any node on which any of the
                                                          selected pods is running.
                                                          Empty topologyKey is not allowed.
                                                        type: string
                                                    required:
                                                    - topologyKey
                                                    type: object
                                                  type: array
                                                  x-kubernetes-list-type: atomic
                                              type: object
                                          type: object
                                        imagePullSecrets:
                                          description: If specified, the pod's imagePullSecrets
                                          items:
                                            description: |-
                                              LocalObjectReference contains enough information to let you locate the
                                              referenced object inside the same namespace.
                                            properties:
                                              name:
                                                default: ""
                                                description: |-
                                                  Name of the referent.
                                                  This field is effectively required, but due to backwards compatibility is
                                                  allowed to be empty. Instances of this type with an empty value here are
                                                  almost certainly wrong.
                                                  More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                                type: string
                                            type: object
                                            x-kubernetes-map-type: atomic
                                          type: array
                                        nodeSelector:
                                          additionalProperties:
                                            type: string
                                          description: |-
                                            NodeSelector is a selector which must be true for the pod to fit on a node.
                                            Selector which must match a node's labels for the pod to be scheduled on that node.
                                            More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
                                          type: object
                                        priorityClassName:
                                          description: If specified, the pod's priorityClassName.
                                          type: string
                                        securityContext:
                                          description: If specified, the pod's security
                                            context
                                          properties:
                                            fsGroup:
                                              description: |-
                                                A special supplemental group that applies to all containers in a pod.
                                                Some volume types allow the Kubelet to change the ownership of that volume
                                                to be owned by the pod:

                                                1. The owning GID will be the FSGroup
                                                2. The setgid bit is set (new files created in the volume will be owned by FSGroup)
                                                3. The permission bits are OR'd with rw-rw----

                                                If unset, the Kubelet will not modify the ownership and permissions of any volume.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              format: int64
                                              type: integer
                                            fsGroupChangePolicy:
                                              description: |-
                                                fsGroupChangePolicy defines behavior of changing ownership and permission of the volume
                                                before being exposed inside Pod. This field will only apply to
                                                volume types which support fsGroup based ownership(and permissions).
                                                It will have no effect on ephemeral volume types such as: secret, configmaps
                                                and emptydir.
                                                Valid values are "OnRootMismatch" and "Always". If not specified, "Always" is used.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              type: string
                                            runAsGroup:
                                              description: |-
                                                The GID to run the entrypoint of the container process.
                                                Uses runtime default if unset.
                                                May also be set in SecurityContext.  If set in both SecurityContext and
                                                PodSecurityContext, the value specified in SecurityContext takes precedence
                                                for that container.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              format: int64
                                              type: integer
                                            runAsNonRoot:
                                              description: |-
                                                Indicates that the container must run as a non-root user.
                                                If true, the Kubelet will validate the image at runtime to ensure that it
                                                does not run as UID 0 (root) and fail to start the container if it does.
                                                If unset or false, no such validation will be performed.
                                                May also be set in SecurityContext.  If set in both SecurityContext and
                                                PodSecurityContext, the value specified in SecurityContext takes precedence.
                                              type: boolean
                                            runAsUser:
                                              description: |-
                                                The UID to run the entrypoint of the container process.
                                                Defaults to user specified in image metadata if unspecified.
                                                May also be set in SecurityContext.  If set in both SecurityContext and
                                                PodSecurityContext, the value specified in SecurityContext takes precedence
                                                for that container.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              format: int64
                                              type: integer
                                            seLinuxOptions:
                                              description: |-
                                                The SELinux context to be applied to all containers.
                                                If unspecified, the container runtime will allocate a random SELinux context for each
                                                container.  May also be set in SecurityContext.  If set in
                                                both SecurityContext and PodSecurityContext, the value specified in SecurityContext
                                                takes precedence for that container.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              properties:
                                                level:
                                                  description: Level is SELinux level
                                                    label that applies to the container.
                                                  type: string
                                                role:
                                                  description: Role is a SELinux role
                                                    label that applies to the container.
                                                  type: string
                                                type:
                                                  description: Type is a SELinux type
                                                    label that applies to the container.
                                                  type: string
                                                user:
                                                  description: User is a SELinux user
                                                    label that applies to the container.
                                                  type: string
                                              type: object
                                            seccompProfile:
                                              description: |-
                                                The seccomp options to use by the containers in this pod.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              properties:
                                                localhostProfile:
                                                  description: |-
                                                    localhostProfile indicates a profile defined in a file on the node should be used.
                                                    The profile must be preconfigured on the node to work.
                                                    Must be a descending path, relative to the kubelet's configured seccomp profile location.
                                                    Must be set if type is "Localhost". Must NOT be set for any other type.
                                                  type: string
                                                type:
                                                  description: |-
                                                    type indicates which kind of seccomp profile will be applied.
                                                    Valid options are:

                                                    Localhost - a profile defined in a file on the node should be used.
                                                    RuntimeDefault - the container runtime default profile should be used.
                                                    Unconfined - no profile should be applied.
                                                  type: string
                                              required:
                                              - type
                                              type: object
                                            supplementalGroups:
                                              description: |-
                                                A list of groups applied to the first process run in each container, in addition
                                                to the container's primary GID, the fsGroup (if specified), and group memberships
                                                defined in the container image for the uid of the container process. If unspecified,
                                                no additional groups are added to any container. Note that group memberships
                                                defined in the container image for the uid of the container process are still effective,
                                                even if they are not included in this list.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              items:
                                                format: int64
                                                type: integer
                                              type: array
                                            sysctls:
                                              description: |-
                                                Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported
                                                sysctls (by the container runtime) might fail to launch.
                                                Note that this field cannot be set when spec.os.name is windows.
                                              items:
                                                description: Sysctl defines a kernel
                                                  parameter to be set
                                                properties:
                                                  name:
                                                    description: Name of a property
                                                      to set
                                                    type: string
                                                  value:
                                                    description: Value of a property
                                                      to set
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                          type: object
                                        serviceAccountName:
                                          description: If specified, the pod's service
                                            account
                                          type: string
                                        tolerations:
                                          description: If specified, the pod's tolerations.
                                          items:
                                            description: |-
                                              The pod this Toleration is attached to tolerates any taint that matches
                                              the triple <key,value,effect> using the matching operator <operator>.
                                            properties:
                                              effect:
                                                description: |-
                                                  Effect indicates the taint effect to match. Empty means match all taint effects.
                                                  When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.
                                                type: string
                                              key:
                                                description: |-
                                                  Key is the taint key that the toleration applies to. Empty means match all taint keys.
                                                  If the key is empty, operator must be Exists; this combination means to match all values and all keys.
                                                type: string
                                              operator:
                                                description: |-
                                                  Operator represents a key's relationship to the value.
                                                  Valid operators are Exists and Equal. Defaults to Equal.
                                                  Exists is equivalent to wildcard for value, so that a pod can
                                                  tolerate all taints of a particular category.
                                                type: string
                                              tolerationSeconds:
                                                description: |-
                                                  TolerationSeconds represents the period of time the toleration (which must be
                                                  of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default,
                                                  it is not set, which means tolerate the taint forever (do not evict). Zero and
                                                  negative values will be treated as 0 (evict immediately) by the system.
                                                format: int64
                                                type: integer
                                              value:
                                                description: |-
                                                  Value is the taint value the toleration matches to.
                                                  If the operator is Exists, the value should be empty, otherwise just a regular string.
                                                type: string
                                            type: object
                                          type: array
                                      type: object
                                  type: object
                                serviceType:
                                  description: |-
                                    Optional service type for Kubernetes solver service. Supported values
                                    are NodePort or ClusterIP. If unset, defaults to NodePort.
                                  type: string
                              type: object
                          type: object
                        selector:
                          description: |-
                            Selector selects a set of DNSNames on the Certificate resource that
                            should be solved using this challenge solver.
                            If not specified, the solver will be treated as the 'default' solver
                            with the lowest priority, i.e. if any other solver has a more specific
                            match, it will be used instead.
                          properties:
                            dnsNames:
                              description: |-
                                List of DNSNames that this solver will be used to solve.
                                If specified and a match is found, a dnsNames selector will take
                                precedence over a dnsZones selector.
                                If multiple solvers match with the same dnsNames value, the solver
                                with the most matching labels in matchLabels will be selected.
                                If neither has more matches, the solver defined earlier in the list
                                will be selected.
                              items:
                                type: string
                              type: array
                            dnsZones:
                              description: |-
                                List of DNSZones that this solver will be used to solve.
                                The most specific DNS zone match specified here will take precedence
                                over other DNS zone matches, so a solver specifying sys.example.com
                                will be selected over one specifying example.com for the domain
                                www.sys.example.com.
                                If multiple solvers match with the same dnsZones value, the solver
                                with the most matching labels in matchLabels will be selected.
                                If neither has more matches, the solver defined earlier in the list
                                will be selected.
                              items:
                                type: string
                              type: array
                            matchLabels:
                              additionalProperties:
                                type: string
                              description: |-
                                A label selector that is used to refine the set of certificate's that
                                this challenge solver will apply to.
                              type: object
                          type: object
                      type: object
                    type: array
                required:
                - privateKeySecretRef
                - server
                type: object
              ca:
                description: |-
                  CA configures this issuer to sign certificates using a signing CA keypair
                  stored in a Secret resource.
                  This is used to build internal PKIs that are managed by cert-manager.
                properties:
                  crlDistributionPoints:
                    description: |-
                      The CRL distribution points is an X.509 v3 certificate extension which identifies
                      the location of the CRL from which the revocation of this certificate can be checked.
                      If not set, certificates will be issued without distribution points set.
                    items:
                      type: string
                    type: array
                  issuingCertificateURLs:
                    description: |-
                      IssuingCertificateURLs is a list of URLs which this issuer should embed into certificates
                      it creates. See https://www.rfc-editor.org/rfc/rfc5280#section-******* for more details.
                      As an example, such a URL might be "http://ca.domain.com/ca.crt".
                    items:
                      type: string
                    type: array
                  ocspServers:
                    description: |-
                      The OCSP server list is an X.509 v3 extension that defines a list of
                      URLs of OCSP responders. The OCSP responders can be queried for the
                      revocation status of an issued certificate. If not set, the
                      certificate will be issued with no OCSP servers set. For example, an
                      OCSP server URL could be "http://ocsp.int-x3.letsencrypt.org".
                    items:
                      type: string
                    type: array
                  secretName:
                    description: |-
                      SecretName is the name of the secret used to sign Certificates issued
                      by this Issuer.
                    type: string
                required:
                - secretName
                type: object
              selfSigned:
                description: |-
                  SelfSigned configures this issuer to 'self sign' certificates using the
                  private key used to create the CertificateRequest object.
                properties:
                  crlDistributionPoints:
                    description: |-
                      The CRL distribution points is an X.509 v3 certificate extension which identifies
                      the location of the CRL from which the revocation of this certificate can be checked.
                      If not set certificate will be issued without CDP. Values are strings.
                    items:
                      type: string
                    type: array
                type: object
              vault:
                description: |-
                  Vault configures this issuer to sign certificates using a HashiCorp Vault
                  PKI backend.
                properties:
                  auth:
                    description: Auth configures how cert-manager authenticates with
                      the Vault server.
                    properties:
                      appRole:
                        description: |-
                          AppRole authenticates with Vault using the App Role auth mechanism,
                          with the role and secret stored in a Kubernetes Secret resource.
                        properties:
                          path:
                            description: |-
                              Path where the App Role authentication backend is mounted in Vault, e.g:
                              "approle"
                            type: string
                          roleId:
                            description: |-
                              RoleID configured in the App Role authentication backend when setting
                              up the authentication backend in Vault.
                            type: string
                          secretRef:
                            description: |-
                              Reference to a key in a Secret that contains the App Role secret used
                              to authenticate with Vault.
                              The `key` field must be specified and denotes which entry within the Secret
                              resource is used as the app role secret.
                            properties:
                              key:
                                description: |-
                                  The key of the entry in the Secret resource's `data` field to be used.
                                  Some instances of this field may be defaulted, in others it may be
                                  required.
                                type: string
                              name:
                                description: |-
                                  Name of the resource being referred to.
                                  More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                type: string
                            required:
                            - name
                            type: object
                        required:
                        - path
                        - roleId
                        - secretRef
                        type: object
                      clientCertificate:
                        description: |-
                          ClientCertificate authenticates with Vault by presenting a client
                          certificate during the request's TLS handshake.
                          Works only when using HTTPS protocol.
                        properties:
                          mountPath:
                            description: |-
                              The Vault mountPath here is the mount path to use when authenticating with
                              Vault. For example, setting a value to `/v1/auth/foo`, will use the path
                              `/v1/auth/foo/login` to authenticate with Vault. If unspecified, the
                              default value "/v1/auth/cert" will be used.
                            type: string
                          name:
                            description: |-
                              Name of the certificate role to authenticate against.
                              If not set, matching any certificate role, if available.
                            type: string
                          secretName:
                            description: |-
                              Reference to Kubernetes Secret of type "kubernetes.io/tls" (hence containing
                              tls.crt and tls.key) used to authenticate to Vault using TLS client
                              authentication.
                            type: string
                        type: object
                      kubernetes:
                        description: |-
                          Kubernetes authenticates with Vault by passing the ServiceAccount
                          token stored in the named Secret resource to the Vault server.
                        properties:
                          mountPath:
                            description: |-
                              The Vault mountPath here is the mount path to use when authenticating with
                              Vault. For example, setting a value to `/v1/auth/foo`, will use the path
                              `/v1/auth/foo/login` to authenticate with Vault. If unspecified, the
                              default value "/v1/auth/kubernetes" will be used.
                            type: string
                          role:
                            description: |-
                              A required field containing the Vault Role to assume. A Role binds a
                              Kubernetes ServiceAccount with a set of Vault policies.
                            type: string
                          secretRef:
                            description: |-
                              The required Secret field containing a Kubernetes ServiceAccount JWT used
                              for authenticating with Vault. Use of 'ambient credentials' is not
                              supported.
                            properties:
                              key:
                                description: |-
                                  The key of the entry in the Secret resource's `data` field to be used.
                                  Some instances of this field may be defaulted, in others it may be
                                  required.
                                type: string
                              name:
                                description: |-
                                  Name of the resource being referred to.
                                  More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                type: string
                            required:
                            - name
                            type: object
                          serviceAccountRef:
                            description: |-
                              A reference to a service account that will be used to request a bound
                              token (also known as "projected token"). Compared to using "secretRef",
                              using this field means that you don't rely on statically bound tokens. To
                              use this field, you must configure an RBAC rule to let cert-manager
                              request a token.
                            properties:
                              audiences:
                                description: |-
                                  TokenAudiences is an optional list of extra audiences to include in the token passed to Vault. The default token
                                  consisting of the issuer's namespace and name is always included.
                                items:
                                  type: string
                                type: array
                              name:
                                description: Name of the ServiceAccount used to request
                                  a token.
                                type: string
                            required:
                            - name
                            type: object
                        required:
                        - role
                        type: object
                      tokenSecretRef:
                        description: TokenSecretRef authenticates with Vault by presenting
                          a token.
                        properties:
                          key:
                            description: |-
                              The key of the entry in the Secret resource's `data` field to be used.
                              Some instances of this field may be defaulted, in others it may be
                              required.
                            type: string
                          name:
                            description: |-
                              Name of the resource being referred to.
                              More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                            type: string
                        required:
                        - name
                        type: object
                    type: object
                  caBundle:
                    description: |-
                      Base64-encoded bundle of PEM CAs which will be used to validate the certificate
                      chain presented by Vault. Only used if using HTTPS to connect to Vault and
                      ignored for HTTP connections.
                      Mutually exclusive with CABundleSecretRef.
                      If neither CABundle nor CABundleSecretRef are defined, the certificate bundle in
                      the cert-manager controller container is used to validate the TLS connection.
                    format: byte
                    type: string
                  caBundleSecretRef:
                    description: |-
                      Reference to a Secret containing a bundle of PEM-encoded CAs to use when
                      verifying the certificate chain presented by Vault when using HTTPS.
                      Mutually exclusive with CABundle.
                      If neither CABundle nor CABundleSecretRef are defined, the certificate bundle in
                      the cert-manager controller container is used to validate the TLS connection.
                      If no key for the Secret is specified, cert-manager will default to 'ca.crt'.
                    properties:
                      key:
                        description: |-
                          The key of the entry in the Secret resource's `data` field to be used.
                          Some instances of this field may be defaulted, in others it may be
                          required.
                        type: string
                      name:
                        description: |-
                          Name of the resource being referred to.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                        type: string
                    required:
                    - name
                    type: object
                  clientCertSecretRef:
                    description: |-
                      Reference to a Secret containing a PEM-encoded Client Certificate to use when the
                      Vault server requires mTLS.
                    properties:
                      key:
                        description: |-
                          The key of the entry in the Secret resource's `data` field to be used.
                          Some instances of this field may be defaulted, in others it may be
                          required.
                        type: string
                      name:
                        description: |-
                          Name of the resource being referred to.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                        type: string
                    required:
                    - name
                    type: object
                  clientKeySecretRef:
                    description: |-
                      Reference to a Secret containing a PEM-encoded Client Private Key to use when the
                      Vault server requires mTLS.
                    properties:
                      key:
                        description: |-
                          The key of the entry in the Secret resource's `data` field to be used.
                          Some instances of this field may be defaulted, in others it may be
                          required.
                        type: string
                      name:
                        description: |-
                          Name of the resource being referred to.
                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                        type: string
                    required:
                    - name
                    type: object
                  namespace:
                    description: |-
                      Name of the vault namespace. Namespaces is a set of features within Vault Enterprise that allows Vault environments to support Secure Multi-tenancy. e.g: "ns1"
                      More about namespaces can be found here https://www.vaultproject.io/docs/enterprise/namespaces
                    type: string
                  path:
                    description: |-
                      Path is the mount path of the Vault PKI backend's `sign` endpoint, e.g:
                      "my_pki_mount/sign/my-role-name".
                    type: string
                  server:
                    description: 'Server is the connection address for the Vault server,
                      e.g: "https://vault.example.com:8200".'
                    type: string
                required:
                - auth
                - path
                - server
                type: object
              venafi:
                description: |-
                  Venafi configures this issuer to sign certificates using a Venafi TPP
                  or Venafi Cloud policy zone.
                properties:
                  cloud:
                    description: |-
                      Cloud specifies the Venafi cloud configuration settings.
                      Only one of TPP or Cloud may be specified.
                    properties:
                      apiTokenSecretRef:
                        description: APITokenSecretRef is a secret key selector for
                          the Venafi Cloud API token.
                        properties:
                          key:
                            description: |-
                              The key of the entry in the Secret resource's `data` field to be used.
                              Some instances of this field may be defaulted, in others it may be
                              required.
                            type: string
                          name:
                            description: |-
                              Name of the resource being referred to.
                              More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                            type: string
                        required:
                        - name
                        type: object
                      url:
                        description: |-
                          URL is the base URL for Venafi Cloud.
                          Defaults to "https://api.venafi.cloud/v1".
                        type: string
                    required:
                    - apiTokenSecretRef
                    type: object
                  tpp:
                    description: |-
                      TPP specifies Trust Protection Platform configuration settings.
                      Only one of TPP or Cloud may be specified.
                    properties:
                      caBundle:
                        description: |-
                          Base64-encoded bundle of PEM CAs which will be used to validate the certificate
                          chain presented by the TPP server. Only used if using HTTPS; ignored for HTTP.
                          If undefined, the certificate bundle in the cert-manager controller container
                          is used to validate the chain.
                        format: byte
                        type: string
                      caBundleSecretRef:
                        description: |-
                          Reference to a Secret containing a base64-encoded bundle of PEM CAs
                          which will be used to validate the certificate chain presented by the TPP server.
                          Only used if using HTTPS; ignored for HTTP. Mutually exclusive with CABundle.
                          If neither CABundle nor CABundleSecretRef is defined, the certificate bundle in
                          the cert-manager controller container is used to validate the TLS connection.
                        properties:
                          key:
                            description: |-
                              The key of the entry in the Secret resource's `data` field to be used.
                              Some instances of this field may be defaulted, in others it may be
                              required.
                            type: string
                          name:
                            description: |-
                              Name of the resource being referred to.
                              More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                            type: string
                        required:
                        - name
                        type: object
                      credentialsRef:
                        description: |-
                          CredentialsRef is a reference to a Secret containing the Venafi TPP API credentials.
                          The secret must contain the key 'access-token' for the Access Token Authentication,
                          or two keys, 'username' and 'password' for the API Keys Authentication.
                        properties:
                          name:
                            description: |-
                              Name of the resource being referred to.
                              More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                            type: string
                        required:
                        - name
                        type: object
                      url:
                        description: |-
                          URL is the base URL for the vedsdk endpoint of the Venafi TPP instance,
                          for example: "https://tpp.example.com/vedsdk".
                        type: string
                    required:
                    - credentialsRef
                    - url
                    type: object
                  zone:
                    description: |-
                      Zone is the Venafi Policy Zone to use for this issuer.
                      All requests made to the Venafi platform will be restricted by the named
                      zone policy.
                      This field is required.
                    type: string
                required:
                - zone
                type: object
            type: object
          status:
            description: Status of the ClusterIssuer. This is set and managed automatically.
            properties:
              acme:
                description: |-
                  ACME specific status options.
                  This field should only be set if the Issuer is configured to use an ACME
                  server to issue certificates.
                properties:
                  lastPrivateKeyHash:
                    description: |-
                      LastPrivateKeyHash is a hash of the private key associated with the latest
                      registered ACME account, in order to track changes made to registered account
                      associated with the Issuer
                    type: string
                  lastRegisteredEmail:
                    description: |-
                      LastRegisteredEmail is the email associated with the latest registered
                      ACME account, in order to track changes made to registered account
                      associated with the  Issuer
                    type: string
                  uri:
                    description: |-
                      URI is the unique account identifier, which can also be used to retrieve
                      account details from the CA
                    type: string
                type: object
              conditions:
                description: |-
                  List of status conditions to indicate the status of a CertificateRequest.
                  Known condition types are `Ready`.
                items:
                  description: IssuerCondition contains condition information for
                    an Issuer.
                  properties:
                    lastTransitionTime:
                      description: |-
                        LastTransitionTime is the timestamp corresponding to the last status
                        change of this condition.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        Message is a human readable description of the details of the last
                        transition, complementing reason.
                      type: string
                    observedGeneration:
                      description: |-
                        If set, this represents the .metadata.generation that the condition was
                        set based upon.
                        For instance, if .metadata.generation is currently 12, but the
                        .status.condition[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the Issuer.
                      format: int64
                      type: integer
                    reason:
                      description: |-
                        Reason is a brief machine readable explanation for the condition's last
                        transition.
                      type: string
                    status:
                      description: Status of the condition, one of (`True`, `False`,
                        `Unknown`).
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: Type of the condition, known values are (`Ready`).
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    categories:
    - cert-manager
    kind: ClusterIssuer
    listKind: ClusterIssuerList
    plural: clusterissuers
    singular: clusterissuer
  conditions:
  - lastTransitionTime: "2025-05-26T11:31:46Z"
    message: no conflicts found
    reason: NoConflicts
    status: "True"
    type: NamesAccepted
  - lastTransitionTime: "2025-05-26T11:31:47Z"
    message: the initial names have been accepted
    reason: InitialNamesAccepted
    status: "True"
    type: Established
  storedVersions:
  - v1
